import React from 'react';
import { NavItem, FooterColumn, WhyUsFeature, FeaturedStory, CustomerTestimonial, Partner, ProductFeatureTab, ClaudeModel } from './types'; // Removed NewsArticle

export const NAV_ITEMS: NavItem[] = [
  { name: '核心产品', href: '#' },
  { name: '安全中心', href: '#' },
  { name: '技术研发', href: '#' },
  { name: '关于我们', href: '#' },
  { name: '最新动态', href: '#' },
  { name: '加入我们', href: '#' },
];

export const FOOTER_COLUMNS: FooterColumn[] = [
  {
    title: '产品',
    links: [
      { name: '版本迭代', href: '#' },
      { name: '定价', href: '#' },
    ],
  },
  {
    title: '公司',
    links: [
      { name: '关于我们', href: '#' },
      { name: '博客', href: '#' },
    ],
  },
  {
    title: '资源',
    links: [
      { name: '服务条款', href: '#' },
      { name: '隐私政策', href: '#' },
      { name: '可接受使用政策', href: '#' },
    ],
  },
];


export const ArrowRightIcon: React.FC<{ className?: string }> = ({ className = "w-4 h-4 ml-1.5" }) => (
  <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={2} stroke="currentColor" className={className}>
    <path strokeLinecap="round" strokeLinejoin="round" d="M13.5 4.5L21 12m0 0l-7.5 7.5M21 12H3" />
  </svg>
);

export const MenuIcon: React.FC<{ className?: string }> = ({ className = "w-6 h-6" }) => (
  <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" className={className}>
    <path strokeLinecap="round" strokeLinejoin="round" d="M3.75 6.75h16.5M3.75 12h16.5m-16.5 5.25h16.5" />
  </svg>
);

export const XIcon: React.FC<{ className?: string }> = ({ className = "w-6 h-6" }) => (
  <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" className={className}>
    <path strokeLinecap="round" strokeLinejoin="round" d="M6 18L18 6M6 6l12 12" />
  </svg>
);

export const AnthropicLogo: React.FC<{className?: string}> = ({className}) => (
  <span className={`font-semibold text-xl tracking-tight ${className}`}>星链AI市值管理</span>
);

export const ExternalLinkIcon: React.FC<{ className?: string }> = ({ className = "w-3 h-3 ml-1 text-neutral-400" }) => (
  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" className={className}>
    <path fillRule="evenodd" d="M4.25 5.5a.75.75 0 00-.75.75v8.5c0 .414.336.75.75.75h8.5a.75.75 0 00.75-.75v-4a.75.75 0 011.5 0v4A2.25 2.25 0 0112.75 17h-8.5A2.25 2.25 0 012 14.75v-8.5A2.25 2.25 0 014.25 4h5a.75.75 0 010 1.5h-5z" clipRule="evenodd" />
    <path fillRule="evenodd" d="M6.25 4.75a.75.75 0 01.75-.75h8.5a.75.75 0 01.75.75v8.5a.75.75 0 01-1.5 0V6.56L6.56 15.56a.75.75 0 11-1.06-1.06L14.44 5.5H7a.75.75 0 01-.75-.75z" clipRule="evenodd" />
  </svg>
);

export const ChevronDownIcon: React.FC<{ className?: string }> = ({ className = "w-5 h-5" }) => (
  <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" className={className}>
    <path strokeLinecap="round" strokeLinejoin="round" d="M19.5 8.25l-7.5 7.5-7.5-7.5" />
  </svg>
);

export const ChevronLeftIcon: React.FC<{ className?: string }> = ({ className = "w-5 h-5" }) => (
  <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" className={className}>
    <path strokeLinecap="round" strokeLinejoin="round" d="M15.75 19.5L8.25 12l7.5-7.5" />
  </svg>
);

export const ChevronRightIcon: React.FC<{ className?: string }> = ({ className = "w-5 h-5" }) => (
  <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" className={className}>
    <path strokeLinecap="round" strokeLinejoin="round" d="M8.25 4.5l7.5 7.5-7.5 7.5" />
  </svg>
);


// Icons for "Why Us?" Section
export const NetworkIcon: React.FC<{ className?: string }> = ({ className = "w-16 h-16 text-neutral-300" }) => (
  <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 64 64" strokeWidth="1.5" stroke="currentColor" className={className}>
    <path strokeLinecap="round" strokeLinejoin="round" d="M32 54C44.1503 54 54 44.1503 54 32C54 19.8497 44.1503 10 32 10C19.8497 10 10 19.8497 10 32C10 44.1503 19.8497 54 32 54Z" strokeWidth="2"/>
    <path strokeLinecap="round" strokeLinejoin="round" d="M32 40C36.4183 40 40 36.4183 40 32C40 27.5817 36.4183 24 32 24C27.5817 24 24 27.5817 24 32C24 36.4183 27.5817 40 32 40Z" strokeWidth="1.5" fill="rgba(100,116,139,0.3)"/>
    <path strokeLinecap="round" strokeLinejoin="round" d="M32 10V2M32 62V54M52.7279 32H62M2 32H11.2721M50.2843 13.7157L56.0416 7.95837M7.95837 56.0416L13.7157 50.2843M50.2843 50.2843L56.0416 56.0416M7.95837 7.95837L13.7157 13.7157" strokeWidth="1.5"/>
  </svg>
);

export const HourglassIcon: React.FC<{ className?: string }> = ({ className = "w-16 h-16 text-neutral-300" }) => (
  <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 64 64" strokeWidth="1.5" stroke="currentColor" className={className}>
    <path strokeLinecap="round" strokeLinejoin="round" d="M14 12H50V20L36 32L50 44V52H14V44L28 32L14 20V12Z" strokeWidth="2" />
    <path strokeLinecap="round" strokeLinejoin="round" d="M18 18H46" strokeWidth="1.5" />
    <path strokeLinecap="round" strokeLinejoin="round" d="M22 26L32 32L42 26" strokeWidth="1.5" opacity="0.7" fill="rgba(100,116,139,0.3)" />
    <path strokeLinecap="round" strokeLinejoin="round" d="M18 46H46" strokeWidth="1.5" />
 </svg>
);

export const LightbulbIcon: React.FC<{ className?: string }> = ({ className = "w-16 h-16 text-neutral-300" }) => (
  <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 64 64" strokeWidth="1.5" stroke="currentColor" className={className}>
    <path strokeLinecap="round" strokeLinejoin="round" d="M32 42C39.732 42 46 35.732 46 28C46 20.268 39.732 14 32 14C24.268 14 18 20.268 18 28C18 35.732 24.268 42 32 42Z" strokeWidth="2" fill="rgba(100,116,139,0.2)"/>
    <path strokeLinecap="round" strokeLinejoin="round" d="M32 42V50H36V54H28V50H32" strokeWidth="2"/>
    <path strokeLinecap="round" strokeLinejoin="round" d="M32 22V20M32 36V34M26 25L24.5 23.5M39.5 32.5L38 31M26 31L24.5 32.5M39.5 23.5L38 25" strokeWidth="1.5"/>
     <path strokeLinecap="round" strokeLinejoin="round" d="M28.6289 28.7901L35.3711 27.2099L34.1165 22.8427L27.3742 24.4229L28.6289 28.7901Z" fill="rgba(200,200,100,0.3)"/>
  </svg>
);

export const WHY_US_FEATURES: WhyUsFeature[] = [
  {
    id: 'data-insights',
    icon: NetworkIcon,
    title: '金融级数据安全',
    description: '数据加密 + 权限管控 = 股东名册绝不外泄',
  },
  {
    id: 'efficient-analysis',
    icon: HourglassIcon,
    title: '一站式AI分析',
    description: '股东变动、路演反馈、市场风险 —— AI提炼分析高价值信息，省掉80%无效阅读',
  },
  {
    id: 'proactive-strategy',
    icon: LightbulbIcon,
    title: '决策辅助而非替代',
    description: 'AI提供量化视角，结合董办专业判断，让人机协同的价值最大化',
  },
];

// Icons for Customer Stories Section
export const SpeechBubbleIcon: React.FC<{ className?: string }> = ({ className = "w-8 h-8" }) => (
  <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" className={className}>
    <path strokeLinecap="round" strokeLinejoin="round" d="M20.25 8.511c.884.284 1.5 1.128 1.5 2.097v4.286c0 1.136-.847 2.1-1.98 2.193-.34.027-.68.052-1.02.072v3.091l-3.696-3.696c-.303-.303-.707-.472-1.128-.472H6.611a2.25 2.25 0 01-2.25-2.25v-6.172c0-.97 1.006-1.705 1.932-1.422l3.722.982.529.14.529-.14 3.722-.982zM15 15.75H9" />
  </svg>
);

export const PlayIcon: React.FC<{ className?: string }> = ({ className = "w-16 h-16" }) => (
  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" className={className}>
    <path fillRule="evenodd" d="M4.5 5.653c0-1.426 1.529-2.33 2.779-1.643l11.54 6.348c1.295.712 1.295 2.573 0 3.285L7.279 19.99c-1.25.687-2.779-.217-2.779-1.643V5.653z" clipRule="evenodd" />
  </svg>
);

// Data for Customer Stories Section
export const FEATURED_STORY_DATA: FeaturedStory = {
  id: 'featured-ai-innovators',
  companyName: 'AI创新科技',
  logo: { src: 'AI创新', alt: 'AI创新科技 Logo', isText: true },
  quote: '“借助星链AI的能力，我们的研发效率提升了60%，更快地将创新想法转化为实际产品。”',
  fullStoryLink: '#',
  mediaUrl: 'https://picsum.photos/seed/customerAI/800/450',
  mediaType: 'video',
};

export const CUSTOMER_TESTIMONIALS_DATA: CustomerTestimonial[] = [
  {
    id: 'testimonial-data-solutions',
    companyName: '数据解决方案公司',
    logo: { src: '数解', alt: '数据解决方案公司 Logo', isText: true },
    testimonial: '“平台的数据分析能力非常强大，帮助我们精准定位了市场趋势。”',
    link: '#',
  },
  {
    id: 'testimonial-smart-manufacturing',
    companyName: '智能制造集团',
    logo: { src: '智造', alt: '智能制造集团 Logo', isText: true },
    testimonial: '“通过AI优化生产流程，我们的运营成本显著降低。”',
    link: '#',
  },
  {
    id: 'testimonial-future-health',
    companyName: '未来健康科技',
    logo: { src: '健科', alt: '未来健康科技 Logo', isText: true },
    testimonial: '“AI辅助诊断系统为我们的医生提供了宝贵的决策支持。”',
  },
];

// Data for OurPartners Section
export const PARTNERS_DATA: Partner[] = [
  { id: 'p1', name: 'Innovatech' },
  { id: 'p2', name: 'Synergy Corp' },
  { id: 'p3', name: 'Future Systems' },
  { id: 'p4', name: 'Quantum Leap' },
  { id: 'p5', name: 'NextGen Solutions' },
  { id: 'p6', name: 'Apex Dynamics' },
  { id: 'p7', name: 'Stellar Ventures' },
  { id: 'p8', name: 'Momentum AI' },
];

// SVG Background for OurPartners Section
export const GalaxyBackground: React.FC<{ className?: string }> = ({ className }) => (
  <div className={`absolute inset-0 overflow-hidden z-0 ${className}`}>
    <svg width="100%" height="100%" preserveAspectRatio="xMidYMid slice" className="opacity-40">
      <defs>
        <radialGradient id="galaxySparkle" cx="50%" cy="50%" r="50%">
          <stop offset="0%" stopColor="rgba(192, 132, 252, 0.7)" />
          <stop offset="50%" stopColor="rgba(192, 132, 252, 0.3)" />
          <stop offset="100%" stopColor="rgba(192, 132, 252, 0)" />
        </radialGradient>
        <linearGradient id="galaxyLine1" x1="0%" y1="50%" x2="100%" y2="50%">
          <stop offset="0%" stopColor="rgba(168, 85, 247, 0.05)" />
          <stop offset="50%" stopColor="rgba(168, 85, 247, 0.3)" />
          <stop offset="100%" stopColor="rgba(168, 85, 247, 0.05)" />
        </linearGradient>
        <linearGradient id="galaxyLine2" x1="0%" y1="50%" x2="100%" y2="50%">
          <stop offset="0%" stopColor="rgba(236, 72, 153, 0.05)" />
          <stop offset="50%" stopColor="rgba(236, 72, 153, 0.25)" />
          <stop offset="100%" stopColor="rgba(236, 72, 153, 0.05)" />
        </linearGradient>
      </defs>
      <path d="M-200 80 Q 150 -30 500 100 T 850 0 T 1200 120 T 1550 50 T 1900 150" stroke="url(#galaxyLine1)" strokeWidth="1.5" fill="none" />
      <path d="M-200 220 Q 150 330 500 200 T 850 300 T 1200 180 T 1550 250 T 1900 150" stroke="url(#galaxyLine2)" strokeWidth="1" fill="none" />
      <circle cx="100" cy="60" r="2.5" fill="url(#galaxySparkle)" opacity="0.8"/>
      <circle cx="450" cy="90" r="2" fill="url(#galaxySparkle)" opacity="0.7"/>
      <circle cx="800" cy="20" r="3" fill="url(#galaxySparkle)" opacity="0.9"/>
      <circle cx="1150" cy="110" r="1.5" fill="url(#galaxySparkle)" opacity="0.6"/>
      <circle cx="1500" cy="70" r="2.5" fill="url(#galaxySparkle)" opacity="0.8"/>
      <circle cx="200" cy="240" r="2" fill="url(#galaxySparkle)" opacity="0.7"/>
      <circle cx="550" cy="180" r="3" fill="url(#galaxySparkle)" opacity="0.9"/>
      <circle cx="900" cy="280" r="1.5" fill="url(#galaxySparkle)" opacity="0.6"/>
      <circle cx="1250" cy="200" r="2.5" fill="url(#galaxySparkle)" opacity="0.8"/>
      <circle cx="1600" cy="230" r="2" fill="url(#galaxySparkle)" opacity="0.7"/>
    </svg>
  </div>
);

// Icons for Product Mockup in OurProductSection
export const HomeIcon: React.FC<{ className?: string }> = ({ className = "w-5 h-5" }) => (
  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" className={className}>
    <path d="M10.707 2.293a1 1 0 00-1.414 0l-7 7a1 1 0 001.414 1.414L4 10.414V17a1 1 0 001 1h2a1 1 0 001-1v-2a1 1 0 011-1h2a1 1 0 011 1v2a1 1 0 001 1h2a1 1 0 001-1v-6.586l.293.293a1 1 0 001.414-1.414l-7-7z" />
  </svg>
);

export const ChartBarIcon: React.FC<{ className?: string }> = ({ className = "w-5 h-5" }) => (
  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" className={className}>
    <path fillRule="evenodd" d="M12 1.5A2.5 2.5 0 009.5 4v12A2.5 2.5 0 0012 18.5h5a2.5 2.5 0 002.5-2.5V4A2.5 2.5 0 0017 1.5h-5zM8 4a2.5 2.5 0 00-2.5 2.5v6.5A2.5 2.5 0 008 15.5h0a2.5 2.5 0 002.5-2.5V6.5A2.5 2.5 0 008 4zM3 8.5a2.5 2.5 0 00-2.5 2.5v2A2.5 2.5 0 003 15.5h0a2.5 2.5 0 002.5-2.5v-2A2.5 2.5 0 003 8.5z" clipRule="evenodd" />
  </svg>
);

export const CogIcon: React.FC<{ className?: string }> = ({ className = "w-5 h-5" }) => (
  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" className={className}>
    <path fillRule="evenodd" d="M11.49 3.17c-.38-1.56-2.6-1.56-2.98 0a1.532 1.532 0 01-2.286.948c-1.372-.836-2.942.734-2.106 2.106.54.886.061 2.042-.947 2.287-1.566.379-1.566 2.6 0 2.978a1.532 1.532 0 01.947 2.287c-.54.886.061 2.042.947 2.287 1.566.379 1.566 2.6 0 2.978a1.532 1.532 0 01-.947 2.287c.836 1.372-.734 2.942-2.106 2.106a1.532 1.532 0 01-2.287.947c-.379 1.566-2.6 1.566-2.978 0a1.533 1.533 0 01-.948-2.287c.836-1.372.734-2.942-2.106-2.106-.54-.886-.061-2.042.947-2.287-1.566-.379-1.566-2.6 0-2.978a1.532 1.532 0 01.947-2.287c.54-.886.061 2.042.947-2.287-1.566-.379-1.566-2.6 0-2.978a1.532 1.532 0 01.947-2.287c-.836-1.372.734 2.942-2.106-2.106a1.532 1.532 0 012.287-.947zM10 13a3 3 0 100-6 3 3 0 000 6z" clipRule="evenodd" />
  </svg>
);

export const FolderIcon: React.FC<{ className?: string }> = ({ className = "w-5 h-5" }) => (
  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" className={className}>
    <path d="M2 6a2 2 0 012-2h5l2 2h5a2 2 0 012 2v6a2 2 0 01-2 2H4a2 2 0 01-2-2V6z" />
  </svg>
);

export const UsersIcon: React.FC<{ className?: string }> = ({ className = "w-5 h-5" }) => (
  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" className={className}>
    <path d="M9 6a3 3 0 11-6 0 3 3 0 016 0zM17 6a3 3 0 11-6 0 3 3 0 016 0zM12.93 17c.046-.327.07-.66.07-1a6.97 6.97 0 00-1.5-4.33A5 5 0 0119 16v1h-6.07zM6 11a5 5 0 015 5v1H1v-1a5 5 0 015-5z" />
  </svg>
);



// Data for ClaudeModelsSection & OurSolution2Section
export const modelsData: ClaudeModel[] = [
  {
    id: 'shareholder-insight',
    name: '股东洞察',
    description: '上传股东名册，自动生成多期对比报告，深度洞悉股东结构变化。',
    features: [
      { name: '自动化多期对比', description: '替代人工整理，自动化多期股东名册数据对比；' },
      { name: '关键变动预警', description: '自动识别新进、退出、增减持股东，高亮核心信号；' },
      { name: 'AI智能报告生成', description: '一键生成基于股东名册的多期分析报告，股东趋势智能总结；' },
    ],
    color: 'purple-500',
    imageUrl: 'https://picsum.photos/seed/insight/600/400',
  },
  {
    id: 'communication-feedback',
    name: '沟通反馈',
    description: '告别逐字听写，AI自动复盘每一次路演，精准把握投资者关切。',
    features: [
      { name: '会议纪要自动化', description: '语音转文字 + 智能纪要生成，释放团队复盘精力；' },
      { name: '关注点智能提炼', description: '基于语义分析生成投资者核心关注点词云与趋势；' },
      { name: '多维度对比分析', description: '支持历史路演反馈纵向对比，助力投关策略迭代；' },
    ],
    color: 'pink-500',
    imageUrl: 'https://picsum.photos/seed/feedback/600/400',
  },
  {
    id: 'market-monitoring',
    name: '市场监测',
    description: '全网聚合信息，主动预警市场风险与机会，赋能市值管理决策。',
    features: [
      { name: '全量信息聚合', description: '自动抓取关联舆情、公告、研报等多维度信息；' },
      { name: '智能风险预警', description: '识别潜在风险信号，分级推送预警；' },
      { name: '机会线索挖掘', description: '从海量信息中捕捉政策红利、行业动态等市值催化因素；' },
    ],
    color: 'teal-500',
    imageUrl: 'https://picsum.photos/seed/monitoring/600/400',
  },
];