"use client";

import { useEffect, useState } from "react";
import { Button } from "@ui/components/button";
import { Input } from "@ui/components/input";
import { 
  Select, 
  SelectContent, 
  SelectItem, 
  SelectTrigger, 
  SelectValue 
} from "@ui/components/select";
import { Upload as UploadIcon, Search as SearchIcon, X as XIcon } from "lucide-react";
import type { RegisterDateItem } from "@saas/shareholder/lib/types";
import { useKeyPress } from "@saas/shareholder/hooks/useKeyPress";
import { useSystemScale } from "@saas/shareholder/hooks/useSystemScale";
import { format } from "date-fns";
import { cn } from "@ui/lib";
import { ColumnFilter, type ColumnOption } from "@saas/shareholder/components/ColumnFilter";

// 添加预设配置类型定义
interface PresetOption {
	key: string;
	title: string;
	columns: string[];
}

interface ShareholderFilterProps {
	registerDate?: string;
	registerDates: RegisterDateItem[];
	isLoadingRegisterDates: boolean;
	onRegisterDateChange: (date: string) => void;
	searchTerm?: string;
	onSearch: (term?: string) => void;
	isLoading?: boolean;
	onImport?: () => void;
	// 新增用于列筛选的属性
	columnOptions?: ColumnOption[];
	visibleColumns?: string[];
	onVisibleColumnsChange?: (columns: string[]) => void;
	defaultColumns?: string[];
	// 新增预设配置相关属性
	presetOptions?: PresetOption[];
	onPresetChange?: (preset: string) => void;
}

/**
 * 股东筛选器组件
 * 提供股东数据的筛选、搜索和导入功能
 * 
 * @version 8.0.0 (2025-05-28) - 移除期数选择中的"最新"标识，简化界面显示
 * @version 7.0.0 (2025-05-22) - 还原筛选按钮，将视图选择移至列显示组件中
 * @version 6.0.0 (2025-05-22) - 添加预设视图选择器，支持快速切换默认视图和全量视图
 * @version 5.0.0 (2025-05-22) - 集成ColumnFilter组件，允许用户筛选显示的表格列
 * @version 4.0.0 (2025-05-22) - 使用SegmentedControl组件替换按钮组，提升用户体验
 * @version 3.0.0 (2025-05-19) - 使用增强版useSystemScale hook进行样式适配，提升代码复用性
 * @version 2.2.0 (2025-05-19) - 添加系统缩放适配，优化不同缩放级别下的显示效果
 * @version 2.1.0 (2025-05-19) - 增加响应式布局支持，优化在小屏幕上的显示
 * @version 2.0.0 (2025-05-19) - 移除移动端适配逻辑，只保留桌面端布局
 */
export function ShareholderFilter({
		registerDate,
		registerDates,
		isLoadingRegisterDates,
		onRegisterDateChange,
		searchTerm,
		onSearch,
		isLoading,
		onImport,
		// 新增列筛选相关属性
		columnOptions,
		visibleColumns,
		onVisibleColumnsChange,
		defaultColumns,
		// 新增预设配置相关属性
		presetOptions,
	}: ShareholderFilterProps) {
		const [inputValue, setInputValue] = useState(searchTerm || "");
		
		// 使用增强版的系统缩放hook获取样式配置
		const { scale, styles, formStyles } = useSystemScale();
		
		// 监听回车键
		useKeyPress(
			"Enter",
			() => {
				onSearch(inputValue || undefined);
			},
			[inputValue, onSearch],
		);

		// 当searchTerm从外部改变时，更新输入框的值
		useEffect(() => {
			setInputValue(searchTerm || "");
		}, [searchTerm]);

		// 处理搜索图标点击
		const handleSearchIconClick = () => {
			onSearch(inputValue || undefined);
		};

		// 处理清除按钮点击
		const handleClearClick = () => {
			setInputValue("");
			onSearch(undefined);
		};

		// 格式化日期展示
		const formatRegisterDate = (dateStr: string) => {
			if (!dateStr) {
				return "";
			}

			try {
				const date = new Date(dateStr);
				if (Number.isNaN(date.getTime())) {
					return dateStr; // 如果不是有效日期，直接返回原字符串
				}
				// 使用更简洁的日期格式，特别是在小屏幕上
				return format(date, "yyyy-MM-dd");
			} catch (error) {
				return dateStr;
			}
		};

		// 格式化期数显示，移除最新标识
		const formatRegisterDateWithLatestTag = (dateStr: string) => {
			// 根据缩放比例使用不同的日期格式
			let formattedDate = formatRegisterDate(dateStr);
			if (scale > 1.25) {
				// 在高缩放比例下使用更精简的日期格式 (只保留月和日)
				try {
					const date = new Date(dateStr);
					if (!Number.isNaN(date.getTime())) {
						formattedDate = format(date, "MM-dd");
					}
				} catch (error) {
					// 保持原格式
				}
			}
			
			return (
				<div className="flex items-center">
					<span className={cn(
						"truncate",
						scale > 1.25 ? "max-w-[90px]" : "max-w-[110px]"
					)}>
						{formattedDate}
					</span>
				</div>
			);
		};

		// 处理输入变化
		const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
			setInputValue(e.target.value);
			// 当输入框清空时，重置搜索
			if (!e.target.value) {
				onSearch(undefined);
			}
		};

		// 渲染日期选择项
		const renderDateOptions = () => {
			if (!Array.isArray(registerDates) || registerDates.length === 0) {
				return <SelectItem value="no-dates" disabled className={cn("font-mono", styles.fontSize.content)}>无可用期数</SelectItem>;
			}
			
			return registerDates.map((item, index) => {
				// 在高缩放模式下使用更简洁的日期格式
				let displayDate = formatRegisterDate(item.registerDate);
				if (scale > 1.25) {
					try {
						const date = new Date(item.registerDate);
						if (!Number.isNaN(date.getTime())) {
							displayDate = format(date, "yyyy-MM-dd");
						}
					} catch (error) {
						// 保持原格式
					}
				}
				
				return (
					<SelectItem
						key={item.registerDate || `register-date-${index}`}
						value={item.registerDate || `register-date-${index}`}
						className={cn(
							"font-mono", 
							styles.fontSize.content,
							scale > 1.25 && "py-1"
						)}
					>
						<div className="flex items-center">
							<span className="truncate">{displayDate}</span>
						</div>
					</SelectItem>
				);
			});
		};

		// 桌面端布局（固定使用）
		return (
			<div>
				<div className="flex flex-wrap items-start justify-between gap-y-4">
					{/* 左侧元素组 */}
					<div
						className={cn(
							"flex flex-wrap items-center max-w-full",
							formStyles.spacing,
						)}
					>
						{/* 期数选择 */}
						<Select
							value={registerDate}
							onValueChange={onRegisterDateChange}
							disabled={
								isLoadingRegisterDates ||
								!Array.isArray(registerDates) ||
								registerDates.length === 0
							}
						>
							<SelectTrigger
								hideIcon={true}
								className={cn(
									formStyles.selectWidth.xs,
									formStyles.selectWidth.sm,
									formStyles.selectWidth.md,
									formStyles.selectWidth.lg,
									"shrink-0",
									"font-mono",
									styles.fontSize.content,
									"h-9 rounded-md", // 更方正的圆角
									scale > 1.25 && "min-h-[2rem] py-1",
									"!w-[120px]", // 减小宽度从150px到120px
								)}
							>
								<SelectValue
									placeholder={
										isLoadingRegisterDates
											? "加载中..."
											: !Array.isArray(registerDates) ||
													registerDates.length === 0
												? "无期数"
												: registerDate
													? formatRegisterDateWithLatestTag(
															registerDate,
														)
													: "选择期数"
									}
								/>
							</SelectTrigger>
							<SelectContent>{renderDateOptions()}</SelectContent>
						</Select>

						{/* 搜索框 */}
						<div className="relative w-full sm:w-[150px] md:w-[180px] lg:w-[200px] xl:w-[250px] shrink">
							<Input
								placeholder="搜索账户名称或证件号码"
								value={inputValue}
								onChange={handleInputChange}
								className={cn(
									"pr-10 w-full", // 恢复原来的padding
									styles.fontSize.content,
									"h-9 rounded-md", // 更方正的圆角
									scale > 1.25
										? styles.elements.inputHeight
										: "",
									"min-h-[2.25rem]", // 确保最小高度与其他元素一致
								)}
								disabled={isLoading}
							/>
							{/* 搜索按钮始终显示 */}
							<button
								type="button"
								className="absolute right-3 top-1/2 transform -translate-y-1/2 h-5 w-5 flex items-center justify-center text-muted-foreground hover:text-foreground focus:outline-none focus:ring-2 focus:ring-primary/30 rounded-full"
								onClick={handleSearchIconClick}
								disabled={isLoading}
								aria-label="搜索"
							>
								<SearchIcon className={formStyles.iconSize} />
							</button>
							
							{/* 清除按钮仅在有输入内容时显示 */}
							{inputValue && (
								<button
									type="button"
									className="absolute right-9 top-1/2 transform -translate-y-1/2 h-5 w-5 flex items-center justify-center text-muted-foreground hover:text-foreground focus:outline-none focus:ring-2 focus:ring-primary/30 rounded-full"
									onClick={handleClearClick}
									disabled={isLoading}
									aria-label="清除搜索"
								>
									<XIcon className={formStyles.iconSize} />
								</button>
							)}
						</div>
					</div>

					{/* 右侧元素组 */}
					<div className="flex flex-wrap items-center gap-3 shrink-0">
						{/* 列显示组件 */}
						{columnOptions && onVisibleColumnsChange && (
							<ColumnFilter
								className={cn(
									"flex items-center gap-1.5 shrink-0",
									styles.fontSize.content,
									"h-9 rounded-md", // 更方正的圆角
									"bg-background text-foreground border-border hover:bg-accent", // 使用默认白色背景
								)}
								columns={columnOptions}
								visibleColumns={visibleColumns || []}
								onVisibleColumnsChange={onVisibleColumnsChange}
								defaultColumns={defaultColumns}
								size={
									formStyles.buttonSize === "icon"
										? "md"
										: formStyles.buttonSize
								}
								presetOptions={presetOptions}
							/>
						)}
						{/* 名册导入按钮 */}
						<Button
							variant="outline"
							size="icon"
							className={cn(
								"shrink-0",
								"h-9 w-9", // 确保高宽一致为9（36px）
								"rounded-md", // 与其他按钮保持一致的圆角
								"bg-background text-foreground border-border hover:bg-accent", // 使用默认白色背景
							)}
							onClick={onImport}
							aria-label="导入名册"
						>
							<UploadIcon className="size-4 m-0" />
						</Button>
					</div>
				</div>
			</div>
		);
	} 