import type { ReactNode } from "react";

export type FAQ = {
	question: ReactNode;
	answer: ReactNode;
};



export type ValuePropositionCardData = {
	challengeTitle: string;
	challengeDescription: string;
	solutionTitle: string;
	icon: ReactNode;
	valuePoints: string[];
	imagePlaceholder: string;
};



export interface NavItem {
	name: string;
	href: string;
}

export interface ModelFeature {
	name: string;
	description: string;
}

export interface ClaudeModel {
	id: string;
	name: string;
	description: string;
	features: ModelFeature[];
	color: string; // Tailwind color class e.g., 'purple-500'
	imageUrl?: string; // Optional image URL for the model
}

export interface Capability {
	id: string;
	title: string;
	description: string;
	details: string;
	imagePlaceholder: string; // URL for placeholder image
}

// export interface NewsArticle { // Removed
//   id: string;
//   title: string;
//   date: string;
//   imageUrl: string;
//   excerpt: string;
//   href: string;
// }

export interface FooterLink {
	name: string;
	href: string;
}

export interface FooterColumn {
	title: string;
	links: FooterLink[];
}

export interface FAQItem {
	id: string;
	question: string;
	answer: string;
}

export interface WhyUsFeature {
	id: string;
	icon: React.FC<{ className?: string }>;
	title: string;
	description: string;
}

export interface CustomerStoryLogo {
	src: string; // URL for image logo, or text if isText is true
	alt: string;
	isText?: boolean;
}

export interface FeaturedStory {
	id: string;
	companyName: string;
	logo: CustomerStoryLogo;
	quote: string;
	fullStoryLink: string;
	mediaUrl: string; // Image or video thumbnail URL
	mediaType: "image" | "video";
}

export interface CustomerTestimonial {
	id: string;
	companyName: string;
	logo: CustomerStoryLogo;
	testimonial: string;
	link?: string;
}

export interface Partner {
	id: string;
	name: string;
	// logoUrl?: string; // Future: for actual image logos
	// logoComponent?: React.FC<{ className?: string }>; // Future: for SVG logos
}

export interface ProductFeatureTab {
	id: string;
	name: string; // For the tab button, e.g., "股东分析"
	mockupImageSrc: string; // URL for the image to display in the main content area of the mockup
	// Optional for more detail:
	// sidebarTitle?: string; // e.g. "Acme Inc." in the mockup sidebar
	// topBarTitle?: string; // e.g. "Company OS / Product" in the mockup top bar
}
