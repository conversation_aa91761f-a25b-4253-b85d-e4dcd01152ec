
import React from 'react';
import SectionWrapper from './SectionWrapper';
import { Button } from '@ui/components/button';
import { ArrowRightIcon } from './constants';

export function CareersSection() {
  return (
    <SectionWrapper 
      className="bg-cover bg-center relative" 
      style={{ backgroundImage: "url('https://picsum.photos/seed/careersbg/1920/1080')" }}
    >
      <div className="absolute inset-0 bg-stone-50/80 backdrop-blur-sm" /> {/* Light theme overlay */}
      {/* Removed py-16 md:py-24 from this div to reduce overall section height */}
      <div className="relative z-10 text-center"> 
        <h2 className="text-3xl sm:text-4xl md:text-5xl font-bold text-neutral-900 mb-6"> {/* Changed text color */}
          加入我们<br className="hidden sm:block" />
          <span className="text-transparent bg-clip-text bg-gradient-to-r from-purple-500 via-pink-500 to-orange-500">开启一站式市值管理</span> {/* Adjusted gradient */}
        </h2>
        <p className="max-w-xl mx-auto text-neutral-700 text-lg mb-10"> {/* Changed text color */}
          立即开始使用星链AI，或与我们的专家团队联系，了解更多定制化解决方案。.
        </p>
        <div className="flex flex-col sm:flex-row items-center justify-center space-y-4 sm:space-y-0 sm:space-x-4">
        <Button variant="primary" size="lg">
          <a href="/">
            开始使用
            <ArrowRightIcon className="w-5 h-5 ml-2" />
          </a>
        </Button>
        <Button variant="outline" size="lg">
          联系我们
        </Button>
      </div>
      </div>
    </SectionWrapper>
  );
};

