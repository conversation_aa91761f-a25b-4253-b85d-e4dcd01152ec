// 数据源配置
datasource db {
  provider = "postgresql" // 使用PostgreSQL数据库
  url      = env("DATABASE_URL") // 从环境变量获取数据库连接URL
}

// Prisma客户端生成器配置
generator client {
  provider = "prisma-client-js" // 生成Prisma客户端代码
}

// Zod类型生成器配置
generator zod {
  provider         = "zod-prisma-types" // 使用zod-prisma-types生成Zod验证模式
  output           = "../src/zod" // 输出目录
  createInputTypes = false // 不生成输入类型
  addIncludeType   = false // 不添加include类型
  addSelectType    = false // 不添加select类型
}

// JSON类型生成器配置
generator json {
  provider = "prisma-json-types-generator" // 生成JSON类型定义
}

// 用户模型
model User {
  id                 String       @id // 用户ID,主键
  name               String // 用户名称
  email              String // 电子邮件
  emailVerified      Boolean // 邮箱是否验证
  image              String? // 用户头像URL,可选
  createdAt          DateTime // 创建时间
  updatedAt          DateTime // 更新时间
  username           String? // 用户名,可选
  role               String? // 用户角色,可选
  banned             Boolean? // 是否被封禁
  banReason          String? // 封禁原因
  banExpires         DateTime? // 封禁到期时间
  onboardingComplete Boolean      @default(false) // 是否完成引导,默认false
  paymentsCustomerId String? // 支付系统客户ID
  locale             String? // 用户语言偏好
  sessions           Session[] // 关联的会话
  accounts           Account[] // 关联的账户
  passkeys           Passkey[] // 关联的密钥
  invitations        Invitation[] // 关联的邀请
  purchases          Purchase[] // 关联的购买记录
  memberships        Member[] // 关联的成员身份
  aiChats            AiChat[] // 关联的AI聊天记录
  shareholderRegistries ShareholderRegistry[] // 关联的股东名册

  @@unique([email]) // 邮箱唯一索引
  @@unique([username]) // 用户名唯一索引
  @@map("user") // 映射到数据库表"user"
}

// 会话模型
model Session {
  id        String   @id // 会话ID,主键
  expiresAt DateTime // 过期时间
  ipAddress String? // IP地址
  userAgent String? // 用户代理
  userId    String // 关联的用户ID
  user      User     @relation(fields: [userId], references: [id], onDelete: Cascade) // 关联用户,级联删除

  impersonatedBy String? // 模拟登录者ID

  activeOrganizationId String? // 当前活动组织ID

  token     String // 会话令牌
  createdAt DateTime // 创建时间
  updatedAt DateTime // 更新时间

  @@unique([token]) // 令牌唯一索引
  @@map("session") // 映射到数据库表"session"
}

// 账户模型
model Account {
  id           String    @id // 账户ID,主键
  accountId    String // 外部账户ID
  providerId   String // 提供商ID
  userId       String // 关联的用户ID
  user         User      @relation(fields: [userId], references: [id], onDelete: Cascade) // 关联用户,级联删除
  accessToken  String? @db.Text // 访问令牌
  refreshToken String? @db.Text // 刷新令牌
  idToken      String? @db.Text // ID令牌
  expiresAt    DateTime? // 过期时间
  password     String? // 密码

  accessTokenExpiresAt  DateTime? // 访问令牌过期时间
  refreshTokenExpiresAt DateTime? // 刷新令牌过期时间
  scope                 String? // 权限范围
  createdAt             DateTime // 创建时间
  updatedAt             DateTime // 更新时间

  @@map("account") // 映射到数据库表"account"
}

// 验证模型
model Verification {
  id         String   @id // 验证ID,主键
  identifier String // 标识符
  value      String   @db.Text // 验证值
  expiresAt  DateTime // 过期时间

  createdAt DateTime? // 创建时间
  updatedAt DateTime? // 更新时间

  @@map("verification") // 映射到数据库表"verification"
}

// 密钥模型
model Passkey {
  id           String    @id // 密钥ID,主键
  name         String? // 密钥名称
  publicKey    String // 公钥
  userId       String // 关联的用户ID
  user         User      @relation(fields: [userId], references: [id], onDelete: Cascade) // 关联用户,级联删除
  credentialID String // 凭证ID
  counter      Int // 计数器
  deviceType   String // 设备类型
  backedUp     Boolean // 是否已备份
  transports   String? // 传输方式
  createdAt    DateTime? // 创建时间

  @@map("passkey") // 映射到数据库表"passkey"
}

// 组织模型
model Organization {
  id                 String       @id // 组织ID,主键
  name               String // 组织名称
  slug               String? // 组织别名
  logo               String? // 组织logo
  createdAt          DateTime // 创建时间
  metadata           String? // 元数据
  paymentsCustomerId String? // 支付系统客户ID
  members            Member[] // 关联的成员
  invitations        Invitation[] // 关联的邀请
  purchases          Purchase[] // 关联的购买记录
  aiChats            AiChat[] // 关联的AI聊天记录

  @@unique([slug]) // 别名唯一索引
  @@map("organization") // 映射到数据库表"organization"

  shareholderRegistries ShareholderRegistry[] // 关联的股东名册
  companyInfos         CompanyInfo[]         // 关联的公司信息
  shareholders         Shareholder[]         // 关联的股东信息
}

// 成员模型
model Member {
  id             String       @id // 成员ID,主键
  organizationId String // 关联的组织ID
  organization   Organization @relation(fields: [organizationId], references: [id], onDelete: Cascade) // 关联组织,级联删除
  userId         String // 关联的用户ID
  user           User         @relation(fields: [userId], references: [id], onDelete: Cascade) // 关联用户,级联删除
  role           String // 角色
  createdAt      DateTime // 创建时间

  @@unique([userId, organizationId]) // 用户ID和组织ID联合唯一索引
  @@map("member") // 映射到数据库表"member"
}

// 邀请模型
model Invitation {
  id             String       @id // 邀请ID,主键
  organizationId String // 关联的组织ID
  organization   Organization @relation(fields: [organizationId], references: [id], onDelete: Cascade) // 关联组织,级联删除
  email          String // 邀请邮箱
  role           String? // 邀请角色
  status         String // 邀请状态
  expiresAt      DateTime // 过期时间
  inviterId      String // 邀请人ID
  user           User         @relation(fields: [inviterId], references: [id], onDelete: Cascade) // 关联用户,级联删除

  @@map("invitation") // 映射到数据库表"invitation"
}

// 购买类型枚举
enum PurchaseType {
  SUBSCRIPTION // 订阅
  ONE_TIME // 一次性购买
}

// 购买模型
model Purchase {
  id             String        @id @default(cuid()) // 购买ID,主键,自动生成
  organization   Organization? @relation(fields: [organizationId], references: [id], onDelete: Cascade) // 关联组织,级联删除
  organizationId String? // 关联的组织ID
  user           User?         @relation(fields: [userId], references: [id], onDelete: Cascade) // 关联用户,级联删除
  userId         String? // 关联的用户ID
  type           PurchaseType // 购买类型
  customerId     String // 客户ID
  subscriptionId String?       @unique // 订阅ID,唯一
  productId      String // 产品ID
  status         String? // 状态
  createdAt      DateTime      @default(now()) // 创建时间,默认当前时间
  updatedAt      DateTime      @updatedAt // 更新时间,自动更新

  @@index([subscriptionId]) // 订阅ID索引
}

// AI聊天模型
model AiChat {
  id             String        @id @default(cuid()) // 聊天ID,主键,自动生成
  organizationId String? // 关联的组织ID
  organization   Organization? @relation(fields: [organizationId], references: [id], onDelete: Cascade) // 关联组织,级联删除
  userId         String? // 关联的用户ID
  user           User?         @relation(fields: [userId], references: [id], onDelete: Cascade) // 关联用户,级联删除
  title          String? // 聊天标题
  /// [AIChatMessages]
  messages       Json? // 聊天消息,JSON格式
  createdAt      DateTime      @default(now()) // 创建时间,默认当前时间
  updatedAt      DateTime      @updatedAt // 更新时间,自动更新
}

//2025-05-08 14:55:59 增加股东模型
// 股东名册表
model ShareholderRegistry {
  id             String         @id @default(cuid()) // 主键,自动生成
  fileName       String         // DBF文件名
  recordCount    Int            // 记录数量
  registerDate     DateTime       // 名册日期
  companyCode    String         // 公司代码
  organizationId String         // 关联的组织ID
  organization   Organization   @relation(fields: [organizationId], references: [id], onDelete: Cascade) // 关联组织，级联删除
  userId         String         // 上传用户ID
  user           User           @relation(fields: [userId], references: [id], onDelete: SetNull) // 关联用户 指定 onDelete 删除用户时不会影响股东名册
  companyInfo    CompanyInfo[]  // 关联的公司信息
  shareholders   Shareholder[]  // 关联的股东信息
  uploadedAt     DateTime       @default(now()) // 上传时间

  @@index([companyCode]) // 公司代码索引
  @@index([organizationId]) // 组织ID索引
  @@index([registerDate]) // 名册日期索引
  @@unique([organizationId, companyCode, registerDate])  // 防止同一组织同一日期重复导入同一公司代码的名册
  @@map("shareholder_registry") // 映射到数据库表"shareholder_registry"
}

// 公司基本信息表
model CompanyInfo {
  id                     String              @id @default(cuid()) // 主键,自动生成
  registryId             String              @default("default_registry_id") // 关联的股东名册ID
  registry               ShareholderRegistry @relation(fields: [registryId], references: [id], onDelete: Cascade) // 关联股东名册，级联删除
  organizationId         String              @default("default_org_id") // 关联的组织ID
  organization           Organization        @relation(fields: [organizationId], references: [id], onDelete: Cascade) // 关联组织，级联删除
  companyCode            String              // 公司代码
  companyName            String              // 公司名称
  registerDate           DateTime            @default(now()) // 名册日期，默认为当前时间
  totalShareholders      Int                 @default(0) // 股东总数，默认为0
  totalInstitutions      Int                 @default(0) // 机构总数，默认为0
  largeShareholdersCount Int                 @default(0) // 大股东数量，默认为0
  largeSharesCount       Decimal             @default(0) @db.Decimal(17, 2) // 大股东持股数量，默认为0
  totalShares            Decimal             @db.Decimal(17, 2) // 总股数
  institutionShares      Decimal             @db.Decimal(17, 2) // 总机构股数
  uploadedAt             DateTime            @default(now()) // 上传时间

  @@index([registryId]) // 名册ID索引
  @@index([companyCode]) // 公司代码索引
  @@index([organizationId]) // 组织ID索引
  @@index([registerDate]) // 名册日期索引
  @@map("company_info") // 映射到数据库表"company_info"
}

// 股东信息表
model Shareholder {
  id                    String              @default(cuid()) // 生成的ID
  shareholderId         String              @default("default_shareholder_id") // 证件号码，作为优先主键
  registryId            String              @default("default_registry_id") // 关联的股东名册ID
  registry              ShareholderRegistry @relation(fields: [registryId], references: [id], onDelete: Cascade) // 关联股东名册，级联删除
  organizationId        String              @default("default_org_id") // 关联的组织ID
  organization          Organization        @relation(fields: [organizationId], references: [id], onDelete: Cascade) // 关联组织，级联删除
  unifiedAccountNumber  String              // 一码通账户号码
  securitiesAccountName String              // 证券账户名称
  shareholderCategory   String              // 持有人类别
  numberOfShares        Decimal             @db.Decimal(17, 2) // 持股数量
  lockedUpShares        Decimal             @db.Decimal(17, 2) // 限售股数量
  shareholdingRatio     Decimal             @db.Decimal(6, 2) // 持股比例
  frozenShares          Decimal             @db.Decimal(17, 2) // 冻结股数
  cashAccount           String?             // 普通证券账户
  sharesInCashAccount   Decimal?            @db.Decimal(17, 2) // 普通账户持股数量
  marginAccount         String?             // 信用证券账户
  sharesInMarginAccount Decimal?            @db.Decimal(17, 2) // 信用账户持股数量
  contactAddress        String?             // 通讯地址
  contactNumber         String?             // 电话号码
  zipCode               String?             // 邮政编码
  relatedPartyIndicator String?             // 关联关系确认标识
  clientCategory        String?             // 客户类别
  marginCollateralAccountNumber  String?    // 汇总账户号码（05名册），新增字段
  marginCollateralAccountName    String?    // 汇总账户名称（05名册），新增字段
  natureOfShares                 String?    // 股份性质（05名册），新增字段
  remarks               String?             // 备注
  registerDate          DateTime            // 名册日期
  uploadedAt            DateTime            @default(now()) // 上传时间

  @@id([shareholderId, id]) // 设置双主键，shareholderId优先
  @@index([registryId]) // 名册ID索引
  @@index([organizationId]) // 组织ID索引
  @@index([unifiedAccountNumber]) // 一码通账户索引
  @@index([securitiesAccountName]) // 证券账户名称索引
  @@index([registerDate]) // 名册日期索引
  @@index([shareholderId, registerDate]) // 组合索引：优化查询相同证件号码在不同名册期的记录
  @@map("shareholder") // 映射到数据库表"shareholder"
}