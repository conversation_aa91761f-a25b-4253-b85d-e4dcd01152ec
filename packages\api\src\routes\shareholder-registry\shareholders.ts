import { Hono } from "hono";
import { db } from "@repo/database";
import { authMiddleware } from "../../middleware/auth";
import { shareholderCryptoMiddleware } from "../../middleware/shareholder-crypto";
import { parseRegisterDate, buildSearchCondition, buildPagination } from "./lib/utils";
import { ShareholdersSchema } from "./lib/validators";
import type { ShareholdersResponse } from "./types";
import { Prisma } from "@prisma/client";

/**
 * 股东列表查询路由
 * 获取特定股东名册下的股东信息，支持分页、搜索和排序
 */
export const shareholdersRouter = new Hono().post(
  "/shareholders",
  authMiddleware,  // 验证用户是否登录
  shareholderCryptoMiddleware(),  // 处理加解密
  async (c) => {
    try {
      // 从context中获取解密后的请求体
      const requestData = c.get("requestData");
      
      // 验证请求数据
      const parsed = ShareholdersSchema.safeParse(requestData);
      if (!parsed.success) {
        c.set("response", {
          code: 400,
          message: `请求参数验证失败：${parsed.error.errors[0]?.message}`,
          data: null
        });
        return;
      }
      
      const {
			registerDate,
			organizationId,
			page = 1,
			limit = 10,
			searchTerm,
			sortBy = "numberOfShares",
			sortOrder = "desc",
		} = parsed.data;

      let registerDateObj: Date;
      
      // 如果未传递registerDate，或者registerDate为空字符串，或者registerDate为null，查询最新的报告日期
      if (!registerDate) {
			const latestRegistry = await db.shareholderRegistry.findFirst({
				where: {
					organizationId,
				},
				orderBy: {
					registerDate: "desc",
				},
				select: {
					registerDate: true,
				},
			});

			if (!latestRegistry) {
				c.set("response", {
					code: 200,
					message: "未找到任何股东名册",
					data: null,
				});
				return;
			}

			registerDateObj = latestRegistry.registerDate;
		} else {
			// 将字符串日期转换为Date对象，用于查询
			registerDateObj = parseRegisterDate(registerDate);
		}
  
      // 查询对应的股东名册ID
      const registry = await db.shareholderRegistry.findFirst({
        where: {
          organizationId,
          registerDate: registerDateObj
        },
        select: {
          id: true
        }
      });

      if (!registry) {
        c.set("response", {
          code: 200,
          message: "未找到对应的股东名册",
          data: null
        });
        return;
      }

      // 构建搜索条件
      const searchCondition = buildSearchCondition(searchTerm);

      // 计算总记录数
      const total = await db.shareholder.count({
        where: {
          registryId: registry.id,
          ...searchCondition
        }
      });

      // 计算分页信息
      const pagination = buildPagination(total, page, limit);

      // 使用原生SQL查询计算排名
      // 创建一个通用的SQL查询来获取带排名的股东数据
      const rankingSql = Prisma.sql`
        WITH ranked_shareholders AS (
          SELECT 
            *, 
            ROW_NUMBER() OVER (ORDER BY "shareholdingRatio" DESC) as rank
          FROM "shareholder"
          WHERE "registryId" = ${registry.id}
        )
        SELECT * FROM ranked_shareholders
        WHERE 1=1
      `;
      
      // 如果有搜索条件，添加到SQL中
      let searchSql = Prisma.sql``;
      if (searchTerm) {
        searchSql = Prisma.sql`
          AND (
            "securitiesAccountName" ILIKE ${`%${searchTerm}%`} OR
            "shareholderId" ILIKE ${`%${searchTerm}%`} OR
            "unifiedAccountNumber" ILIKE ${`%${searchTerm}%`} OR
            "contactNumber" ILIKE ${`%${searchTerm}%`}
          )
        `;
      }
      
      // 添加排序和分页条件
      const paginationSql = Prisma.sql`
        ORDER BY ${Prisma.raw(`"${sortBy}" ${sortOrder}`)}
        LIMIT ${limit}
        OFFSET ${(page - 1) * limit}
      `;
      
      // 执行查询
      const shareholders = await db.$queryRaw<any[]>(
        Prisma.sql`${rankingSql} ${searchSql} ${paginationSql}`
      );

      // 转换查询结果为响应格式
      const responseData: ShareholdersResponse = {
        shareholders: shareholders.map(item => ({
          id: item.id,
          shareholderId: item.shareholderId,
          unifiedAccountNumber: item.unifiedAccountNumber,
          securitiesAccountName: item.securitiesAccountName,
          shareholderCategory: item.shareholderCategory,
          numberOfShares: item.numberOfShares.toString(),
          lockedUpShares: item.lockedUpShares.toString(),
          shareholdingRatio: item.shareholdingRatio.toString(),
          frozenShares: item.frozenShares.toString(),
          contactAddress: item.contactAddress || undefined,
          contactNumber: item.contactNumber || undefined,
          zipCode: item.zipCode || undefined,
          cashAccount: item.cashAccount || undefined,
          sharesInCashAccount: item.sharesInCashAccount?.toString() || undefined,
          marginAccount: item.marginAccount || undefined,
          sharesInMarginAccount: item.sharesInMarginAccount?.toString() || undefined,
          relatedPartyIndicator: item.relatedPartyIndicator || undefined,
          clientCategory: item.clientCategory || undefined,
          remarks: item.remarks || undefined,
          rank: Number(item.rank) // 添加排名字段
        })),
        pagination
      };
      // 设置响应，中间件会自动处理加密
      c.set("response", {
        code: 200,
        message: "获取股东列表成功",
        data: responseData
      });

      return;
    } catch (error) {
      // 处理错误情况
      console.error("[股东名册-股东列表查询] 错误:", error);
      c.set("response", {
        code: 500,
        message: "获取股东列表失败",
        data: null
      });
      return;
    }
  }
); 