
import React from 'react';
import { FOOTER_COLUMNS, AnthropicLogo, ExternalLinkIcon } from './constants';
import type { FooterColumn } from '../types';

const SocialIcon: React.FC<{ href: string, children: React.ReactNode, label: string }> = ({ href, children, label }) => (
  <a href={href} target="_blank" rel="noopener noreferrer" aria-label={label} className="text-neutral-500 hover:text-purple-600 transition-colors"> {/* Changed color */}
    {children}
  </a>
);

export function Footer() {
  return (
    <footer className="bg-stone-100 border-t border-neutral-200"> 
      <div className="container mx-auto px-4 sm:px-6 lg:px-8 max-w-7xl py-12 md:py-16">
        <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-8 mb-12">
          <div className="col-span-2 md:col-span-4 lg:col-span-2 mb-8 lg:mb-0">
            <a href="\" className="inline-block mb-4">
              <AnthropicLogo className="text-neutral-800 hover:text-purple-600 transition-colors" /> 
            </a>
            <p className="text-neutral-600 text-sm max-w-xs"> 
              AI驱动一站式市值管理
            </p>
             <div className="flex space-x-5 mt-6">
              <SocialIcon href="#" label="Twitter / X">
                <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 24 24" aria-hidden="true"><path d="M18.244 2.25h3.308l-7.227 8.26 8.502 11.24H16.17l-5.214-6.817L4.99 21.75H1.68l7.73-8.835L1.254 2.25H8.08l4.713 6.231zm-1.161 17.52h1.833L7.084 4.126H5.117z"/></svg>
              </SocialIcon>
              <SocialIcon href="#" label="LinkedIn">
                <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 24 24" aria-hidden="true"><path fillRule="evenodd" d="M19 0h-14c-2.761 0-5 2.239-5 5v14c0 2.761 2.239 5 5 5h14c2.762 0 5-2.239 5-5v-14c0-2.761-2.238-5-5-5zm-11 19h-3v-11h3v11zm-1.5-12.268c-.966 0-1.75-.79-1.75-1.764s.784-1.764 1.75-1.764 1.75.79 1.75 1.764-.783 1.764-1.75 1.764zm13.5 12.268h-3v-5.604c0-3.368-4-3.113-4 0v5.604h-3v-11h3v1.765c1.396-2.586 7-2.777 7 2.476v6.759z" clipRule="evenodd"/></svg>
              </SocialIcon>
            </div>
          </div>

          {FOOTER_COLUMNS.map((column: FooterColumn) => (
            <div key={column.title}>
              <h3 className="text-sm font-semibold text-neutral-700 tracking-wider uppercase mb-4">{column.title}</h3> 
              <ul className="space-y-3">
                {column.links.map((link) => (
                  <li key={link.name}>
                    <a href={link.href} className="text-sm text-neutral-600 hover:text-purple-600 hover:underline transition-colors flex items-center"> {/* Changed color */}
                      {link.name}
                      {link.href.startsWith('http') && <ExternalLinkIcon className="w-3 h-3 ml-1 text-neutral-400 group-hover:text-purple-500"/>} {/* Adjusted icon color */}
                    </a>
                  </li>
                ))}
              </ul>
            </div>
          ))}
        </div>

        <div className="border-t border-neutral-300 pt-8 text-center md:text-left">
          <p className="text-sm text-neutral-500"> 
            &copy; {new Date().getFullYear()} 星链AI市值管理平台. 保留所有权利.
          </p>
        </div>
      </div>
    </footer>
  );
};