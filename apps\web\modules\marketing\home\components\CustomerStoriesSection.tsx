
import React from 'react';
import SectionWrapper from './SectionWrapper';
import { Button } from '@ui/components/button';
import { FEATURED_STORY_DATA, CUSTOMER_TESTIMONIALS_DATA, SpeechBubbleIcon, PlayIcon, ArrowRightIcon } from './constants';
import type { CustomerStoryLogo } from '../types';

const LogoDisplay: React.FC<{ logo: CustomerStoryLogo, companyName: string, className?: string, textClassName?: string }> = ({ logo, companyName, className, textClassName }) => {
  if (logo.isText) {
    return <span className={`font-bold text-xl ${textClassName || 'text-neutral-700'}`}>{logo.src}</span>; // Light theme text
  }
  return <img src={logo.src} alt={`${companyName} Logo`} className={`h-8 md:h-10 object-contain ${className}`} />;
};


export function CustomerStoriesSection() {
  const featured = FEATURED_STORY_DATA;
  const testimonials = CUSTOMER_TESTIMONIALS_DATA;

  return (
			<SectionWrapper className="bg-stone-50 dotted-bg">
				{" "}
				{/* Changed background */}
				<div className="flex items-center mb-10 md:mb-12">
					<SpeechBubbleIcon className="w-7 h-7 md:w-8 md:h-8 text-purple-500 mr-3" />{" "}
					{/* Adjusted color */}
					<h2 className="text-3xl sm:text-4xl md:text-5xl font-bold text-neutral-900">
						{" "}
						{/* Changed text color */}
						用户
						<span className="text-transparent bg-clip-text bg-gradient-to-r from-purple-500 to-pink-500">
							评价
						</span>{" "}
						{/* Adjusted gradient */}
					</h2>
				</div>
				{/* Featured Story */}
				<div className="bg-white p-6 md:p-8 lg:p-10 rounded-xl shadow-xl border border-neutral-200/70 flex flex-col md:flex-row gap-6 md:gap-8 lg:gap-10 mb-12 md:mb-16 items-center">
					{" "}
					{/* Light theme card */}
					<div className="md:w-1/2 lg:w-3/5 space-y-5 md:space-y-6">
						<div className="flex items-center space-x-3">
							<LogoDisplay
								logo={featured.logo}
								companyName={featured.companyName}
								textClassName="text-2xl md:text-3xl text-purple-500"
							/>{" "}
							{/* Adjusted logo text color */}
							<h3 className="text-2xl md:text-3xl font-bold text-neutral-800">
								{featured.companyName}
							</h3>{" "}
							{/* Changed text color */}
						</div>
						<blockquote className="text-2xl md:text-3xl lg:text-4xl font-medium text-neutral-700 leading-tight md:leading-tight">
							{" "}
							{/* Changed text color */}
							{featured.quote}
						</blockquote>
						<Button
							asChild
							variant="link"
							className="text-purple-600 text-base md:text-lg"
						>
							<a href={featured.fullStoryLink}>
								阅读完整故事
								<ArrowRightIcon className="w-4 h-4 ml-1.5" />
							</a>
						</Button>
					</div>
					<div className="md:w-1/2 lg:w-2/5 w-full">
						<div className="relative aspect-video rounded-lg overflow-hidden shadow-lg group">
							<img
								src={featured.mediaUrl}
								alt={`${featured.companyName} story media`}
								className="w-full h-full object-cover transition-transform duration-300 group-hover:scale-105"
							/>
							{featured.mediaType === "video" && (
								<div className="absolute inset-0 flex items-center justify-center bg-black/20">
									{" "}
									{/* Slightly lighter overlay for video */}
									<PlayIcon className="w-16 h-16 text-white/90 group-hover:text-white transition-colors" />
								</div>
							)}
						</div>
					</div>
				</div>
				{/* Testimonials Grid */}
				<div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6 md:gap-8">
					{testimonials.map((testimonial) => (
						<div
							key={testimonial.id}
							className="bg-white p-5 md:p-6 rounded-lg shadow-lg border border-neutral-200/70 flex flex-col items-start hover:shadow-purple-500/10 hover:border-purple-400/60 transition-all duration-300 transform hover:-translate-y-1" // Light theme card
						>
							<div className="flex items-center space-x-3 mb-3">
								<LogoDisplay
									logo={testimonial.logo}
									companyName={testimonial.companyName}
									textClassName="text-lg text-purple-500"
								/>{" "}
								{/* Adjusted logo text color */}
								<h4 className="font-semibold text-neutral-800 text-lg">
									{testimonial.companyName}
								</h4>{" "}
								{/* Changed text color */}
							</div>
							<p className="text-neutral-600 text-sm md:text-base leading-relaxed mb-4 flex-grow">
								{" "}
								{/* Changed text color */}“
								{testimonial.testimonial}”
							</p>
							{testimonial.link && (
								<a
									href={testimonial.link}
									target="_blank"
									rel="noopener noreferrer"
									className="inline-flex items-center text-xs text-purple-600 hover:text-purple-500 hover:underline mt-auto" // Adjusted link color
								>
									了解更多{" "}
									<ArrowRightIcon className="w-3 h-3 ml-1" />
								</a>
							)}
						</div>
					))}
				</div>
			</SectionWrapper>
		);
};
