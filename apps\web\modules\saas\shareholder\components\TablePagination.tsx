"use client";

import { useState, useEffect, useRef } from "react";
import { Button } from "@ui/components/button";
import { Input } from "@ui/components/input";
import type { PaginationInfo } from "@saas/shareholder/lib/types";
import { toast } from "sonner";

interface TablePaginationProps {
  pagination: PaginationInfo;
  page: number;
  onPageChange: (page: number) => void;
  onLimitChange?: (limit: number) => void;
}

/**
 * 表格分页组件
 * 提供数据分页导航功能
 * 
 * @version 2.6.0 (2025-05-23) - 修复输入框在未聚焦状态下的字体颜色问题
 * @version 2.5.0 (2025-05-23) - 修复输入框字体颜色问题，确保始终为黑色，优化回车键触发逻辑
 * @version 2.4.0 (2025-05-22) - 减小上下间距，修复输入框字体颜色，优化键盘回车确认
 * @version 2.3.0 (2025-05-22) - 移除缩放适应功能，字体颜色改为黑色
 * @version 2.2.0 (2025-05-21) - 添加了每页条数自定义输入框功能
 * @version 2.1.0 (2025-05-21) - 添加系统缩放适配，优化高缩放比例下的显示效果
 * @version 2.0.0 (2025-05-19) - 移除移动端适配逻辑，只保留桌面端布局
 */
export function TablePagination({
  pagination,
  page,
  onPageChange,
  onLimitChange
}: TablePaginationProps) {
  // 跳页输入框状态
  const [jumpToPage, setJumpToPage] = useState("");
  
  // 每页条数输入框状态
  const [itemsPerPageInput, setItemsPerPageInput] = useState("");
  
  // 创建样式元素的引用
  const styleRef = useRef<HTMLStyleElement | null>(null);
  
  // 添加全局样式以确保输入框文字颜色为黑色
  useEffect(() => {
    // 创建样式元素
    const styleElement = document.createElement('style');
    styleElement.textContent = `
      .pagination-input,
      .pagination-input::placeholder,
      .pagination-input:focus,
      .pagination-input:not(:focus) {
        color: black !important;
      }
      
      .pagination-input input {
        color: black !important;
      }
    `;
    
    // 将样式添加到文档头部
    document.head.appendChild(styleElement);
    
    // 保存引用以便在组件卸载时移除
    styleRef.current = styleElement;
    
    // 组件卸载时移除样式
    return () => {
      if (styleRef.current) {
        document.head.removeChild(styleRef.current);
      }
    };
  }, []);
  
  // 处理跳转到特定页
  const handleJumpToPage = () => {
    const pageNum = Number.parseInt(jumpToPage, 10);
    // 检查是否为有效页码
    if (!Number.isNaN(pageNum) && pageNum > 0 && pagination && pageNum <= pagination.totalPages) {
      onPageChange(pageNum);
      // 只有在成功跳转后才清空输入框
      setJumpToPage("");
    } else if (jumpToPage !== "") {
      // 如果输入了无效页码，显示提示并清空输入框
      toast.error("页码无效", {
        description: `请输入1-${pagination?.totalPages || 1}之间的页码`,
        duration: 3000,
      });
      setJumpToPage("");
    }
  };

  // 处理输入变化
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    // 只允许输入数字
    const value = e.target.value.replace(/\D/g, '');
    setJumpToPage(value);
  };
  
  // 处理每页显示条数的变更
  const handleItemsPerPageChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    // 只允许输入数字
    const value = e.target.value.replace(/\D/g, '');
    setItemsPerPageInput(value);
  };
  
  // 处理每页条数确认
  const handleLimitChange = () => {
    const limit = Number.parseInt(itemsPerPageInput, 10);
    // 检查是否为有效条数（至少10条，最多100条）
    if (!Number.isNaN(limit) && limit >= 10 && limit <= 100 && onLimitChange) {
      onLimitChange(limit);
      // 只有在成功设置后才清空输入框
      setItemsPerPageInput("");
    } else if (!Number.isNaN(limit) && (limit < 10 || limit > 100)) {
      // 如果输入的值超出范围，给用户反馈
      toast.error("每页条数无效", {
        description: "每页显示条数必须在10-100之间",
        duration: 3000,
      });
      setItemsPerPageInput("");
    }
  };
  
  // 键盘事件处理函数
  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === "Enter") {
      // 阻止事件冒泡，防止表单提交
      e.preventDefault();
      e.stopPropagation();
      
      if (jumpToPage) {
        handleJumpToPage();
      } else if (itemsPerPageInput) {
        handleLimitChange();
      }
    }
  };
  
  const totalPages = pagination?.totalPages || 1;
  const totalItems = pagination?.total || 0;
  const itemsPerPage = pagination?.limit || 20;
  
  // 使用桌面端布局
		return (
			<div className="flex items-center justify-between border-gray-200 px-2 text-xs">
				{/* 左侧翻页按钮 */}
				<div className="flex items-center">
					<Button
						variant="outline"
						size="icon"
						className="border-gray-200 text-primary rounded-sm h-7 w-7"
						onClick={() => onPageChange(page - 1)}
						disabled={page <= 1}
					>
						<span className="text-lg" style={{ lineHeight: "1" }}>
							&lt;
						</span>
					</Button>
					<Button
						variant="outline"
						size="icon"
						className="border-gray-200 ml-2 text-primary rounded-sm h-7 w-7"
						onClick={() => onPageChange(page + 1)}
						disabled={page >= totalPages}
					>
						<span className="text-lg" style={{ lineHeight: "1" }}>
							&gt;
						</span>
					</Button>
				</div>

				{/* 右侧页码信息 */}
				<div className="flex items-center text-black">
					<span>第</span>
					<div
						className="relative mx-1 w-8 [&_input]:text-black"
					>
						<Input
							className="pagination-input w-full text-center pl-1 pr-0 rounded-sm h-6 text-xs text-black [&>*]:text-black [&::placeholder]:text-black !text-black"
							value={jumpToPage}
							placeholder={String(page)}
							onChange={handleInputChange}
							onKeyDown={handleKeyDown}
							onBlur={handleJumpToPage}
							style={{ 
								color: "black", 
								caretColor: "black"
							}}
						/>
					</div>
					<span>页/共 {totalPages} 页</span>

					{onLimitChange && (
						<>
							<span className="mx-2">每页</span>
							<div
								className="relative mx-1 w-12 [&_input]:text-black"
							>
								<Input
									className="pagination-input w-full text-center pl-1 pr-0 rounded-sm h-6 text-xs text-black [&>*]:text-black [&::placeholder]:text-black !text-black"
									value={itemsPerPageInput}
									placeholder={String(itemsPerPage)}
									onChange={handleItemsPerPageChange}
									onKeyDown={handleKeyDown}
									onBlur={handleLimitChange}
									style={{ 
										color: "black", 
										caretColor: "black"
									}}
								/>
							</div>
							<span>条</span>
						</>
					)}

					{!onLimitChange && (
						<span className="mx-2">每页{itemsPerPage}条</span>
					)}

					<span className="mx-2">共{totalItems}条</span>
				</div>
			</div>
		);
} 