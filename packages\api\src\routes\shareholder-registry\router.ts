import { Hono } from "hono";
import { uploadRouter } from "./upload";
import { listRouter } from "./list";
import { registerDatesRouter } from "./register-dates";
import { shareholdersRouter } from "./shareholders";
import { deleteRouter } from "./delete";

/**
 * 股东名册主路由
 * 聚合所有股东名册相关的子路由
 */
export const shareholderRegistryRouter = new Hono()
  .basePath("/shareholder-registry")
  .route("/", uploadRouter)
  .route("/", listRouter)
  .route("/", registerDatesRouter)
  .route("/", shareholdersRouter)
  .route("/", deleteRouter); 