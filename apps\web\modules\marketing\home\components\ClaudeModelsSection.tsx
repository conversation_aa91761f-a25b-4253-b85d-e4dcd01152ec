import { <PERSON><PERSON> } from "@ui/components/button";
import { ArrowRightIcon, CheckIcon } from "lucide-react";
import Link from "next/link";
import type { ClaudeModel } from "../types";
import SectionWrapper from "./SectionWrapper";
import { modelsData } from "./constants";

interface ModelCardProps {
	model: ClaudeModel;
}

const ModelCard: React.FC<ModelCardProps> = ({ model }) => {
	// Determine gradient colors based on model.color
	let gradientToColor = 'pink-500'; // Default
	if (model.color === 'purple-500') {
		gradientToColor = 'pink-500';
	} else if (model.color === 'pink-500') {
		gradientToColor = 'orange-500';
	} else if (model.color === 'teal-500') {
		gradientToColor = 'cyan-500';
	}

	return (
		<div className={`bg-white rounded-xl p-6 md:p-8 shadow-lg hover:shadow-xl hover:shadow-${model.color}/20 transition-all duration-300 flex flex-col h-full border border-neutral-200 hover:border-${model.color} transform hover:-translate-y-1`}>
			<h3 className={`text-2xl md:text-3xl font-bold mb-3 text-transparent bg-clip-text bg-gradient-to-r from-${model.color} via-${gradientToColor} to-${model.color}`}>
				{model.name}
			</h3>
			<p className="text-neutral-600 mb-6 text-sm md:text-base flex-grow">
				{model.description}
			</p>
			<ul className="space-y-3 mb-6">
				{model.features.map(feature => (
					<li key={feature.name} className="flex items-start">
						<CheckIcon className={`w-5 h-5 text-${model.color} mr-2 mt-1 flex-shrink-0`} />
						<span className="text-neutral-700 text-sm">
							<span className="font-medium">{feature.name}</span>: <span className="text-neutral-500">{feature.description}</span>
						</span>
					</li>
				))}
			</ul>
			<Button variant="link" className={`mt-auto self-start text-${model.color}`} asChild>
				<Link href="#">
					了解更多详情
					<ArrowRightIcon className="ml-2 size-4" />
				</Link>
			</Button>
		</div>
	);
};

// Sample data for the models



export function ClaudeModelsSection() {
	return (
		<SectionWrapper className="bg-purple-50 dotted-bg">
			<div className="text-center mb-12 md:mb-16">
				<h2 className="text-3xl sm:text-4xl md:text-5xl font-bold text-neutral-900 mb-4">
					产品和服务<span className="text-transparent bg-clip-text bg-gradient-to-r from-purple-500 to-pink-500">产品和服务</span>
				</h2>
				<p className="max-w-2xl mx-auto text-neutral-600 text-lg">
					融合尖端AI技术，为您的市值管理提供全方位智能支持。
				</p>
			</div>
			<div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
				{modelsData.map((model) => (
					<ModelCard key={model.id} model={model} />
				))}
			</div>
			<div className="mt-12 md:mt-16 text-center">
				<Button variant="primary" size="lg" asChild>
					<Link href="#">
						探索所有解决方案
						<ArrowRightIcon className="ml-2 size-4" />
					</Link>
				</Button>
			</div>
		</SectionWrapper>
	);
}