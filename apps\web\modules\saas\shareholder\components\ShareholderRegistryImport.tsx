"use client";

import { useState, useCallback, useRef, useEffect } from "react";
import { But<PERSON> } from "@ui/components/button";
import { Input } from "@ui/components/input";
import { Label } from "@ui/components/label";
import { 
  Card, 
  CardContent, 
  CardDescription, 
  CardHeader, 
  CardTitle 
} from "@ui/components/card";
import { 
  FileUpIcon, 
  UploadCloudIcon, 
  CheckCircleIcon,
  Loader2Icon,
  XIcon,
  AlertCircleIcon,
  FileTypeIcon,
  FilesIcon
} from "lucide-react";
import { Alert, AlertDescription, AlertTitle } from "@ui/components/alert";
import { Progress } from "@ui/components/progress";
import { useShareholderRegistry } from "@saas/shareholder/hooks/useShareholders";
import { toast } from "sonner";
import { parseShareholderRegistryFile, isSupportedFileType } from "@saas/shareholder/lib/file-parser";
import type { ShareholderRegistryParseResult } from "@saas/shareholder/lib/dbf-parser";
import { ShareholderRegistryHelpDialog } from "./dialogs";
import { ShareholderRegistryImportErrorDialog } from "./dialogs/ShareholderRegistryImportErrorDialog";
import { ShareholderRegistryImportProgress } from "./ShareholderRegistryImportProgress";

interface ShareholderRegistryImportProps {
  organizationId: string;
  onComplete?: () => void;
}

/**
 * 股东名册导入表单组件
 * 支持拖拽上传和文件选择
 * 仅处理DBF文件解析和验证
 * 支持上传多个文件
 * @update 2025-05-28 更新为支持DBF、XLS、XLSX和ZIP文件格式
 * @update 2025-05-26 优化了上传进度条，在调用API前开始模拟进度，在90%处等待后端响应，给用户更好的反馈
 * @update 2025-05-20 更新了导入逻辑，优化了导入体验
 * @update 2025-05-19 更新了校验流程，优化了导入体验
 * @update 2025-05-19 更新错误处理流程，统一展示错误信息
 */
export function ShareholderRegistryImport({
	organizationId,
	onComplete,
}: ShareholderRegistryImportProps) {
	// 修改为支持多文件
	const [files, setFiles] = useState<File[]>([]);
	const [currentFileIndex, setCurrentFileIndex] = useState<number>(-1);
	const [isDragging, setIsDragging] = useState(false);
	const [error, setError] = useState<string | null>(null);
	const [success, setSuccess] = useState(false);
	const [uploadProgress, setUploadProgress] = useState(0);
	const [importPhase, setImportPhase] = useState<
		"idle" | "validating" | "importing"
	>("idle");
	const [parseResults, setParseResults] = useState<
		ShareholderRegistryParseResult[]
	>([]);
	const [showHelpDialog, setShowHelpDialog] = useState(false);
	const [validationError, setValidationError] = useState<{
		message: string;
		fileIndex: number;
		fileName?: string;
	} | null>(null);

	// 进度定时器引用
	const progressTimerRef = useRef<NodeJS.Timeout | null>(null);

	// 使用自定义钩子来处理上传
	const { uploadRegistry, isUploading } =
		useShareholderRegistry(organizationId);

	// 处理文件选择
	const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
		if (!e.target.files?.length) {
			setError(
				"请选择有效的DBF文件，且文件大小不超过20MB",
			);
			return;
		}

		// 转换FileList为数组并过滤掉不支持的文件格式
		const selectedFiles = Array.from(e.target.files).filter(
			(file) =>
				isSupportedFileType(file.name) &&
				file.size <= 20 * 1024 * 1024,
		);

		if (selectedFiles.length === 0) {
			setError(
				"请选择有效的DBF文件，且文件大小不超过20MB",
			);
			return;
		}

		// 如果当前有文件且处于闲置状态，则追加新文件而不是替换
		if (files.length > 0 && importPhase === "idle") {
			// 检查是否有重复文件（根据文件名和大小判断）
			const existingFileNames = files.map(f => `${f.name}-${f.size}`);
			const newFiles = selectedFiles.filter(
				file => !existingFileNames.includes(`${file.name}-${file.size}`)
			);
			
			if (newFiles.length === 0) {
				setError("所选文件已在列表中");
				return;
			}
			
			// 追加新文件
			setFiles(prevFiles => [...prevFiles, ...newFiles]);
			// 清除可能的错误信息，但保留其他状态
			setError(null);
		} else {
			// 如果当前没有文件或者不是闲置状态，则重置状态并设置新文件
			resetState();
			setFiles(selectedFiles);
		}
	};

	// 处理拖拽开始
	const handleDragOver = (e: React.DragEvent<HTMLDivElement>) => {
		e.preventDefault();
		setIsDragging(true);
	};

	// 处理拖拽离开
	const handleDragLeave = () => {
		setIsDragging(false);
	};

	// 处理拖拽放置
	const handleDrop = (e: React.DragEvent<HTMLDivElement>) => {
		e.preventDefault();
		setIsDragging(false);

		// 获取拖拽的文件并过滤
		const droppedFiles = Array.from(e.dataTransfer.files).filter(
			(file) =>
				isSupportedFileType(file.name) &&
				file.size <= 20 * 1024 * 1024,
		);

		if (droppedFiles.length === 0) {
			setError(
				"请选择有效的DBF文件，且文件大小不超过20MB",
			);
			return;
		}

		// 如果当前有文件且处于闲置状态，则追加新文件而不是替换
		if (files.length > 0 && importPhase === "idle") {
			// 检查是否有重复文件（根据文件名和大小判断）
			const existingFileNames = files.map(f => `${f.name}-${f.size}`);
			const newFiles = droppedFiles.filter(
				file => !existingFileNames.includes(`${file.name}-${file.size}`)
			);
			
			if (newFiles.length === 0) {
				setError("所选文件已在列表中");
				return;
			}
			
			// 追加新文件
			setFiles(prevFiles => [...prevFiles, ...newFiles]);
			// 清除可能的错误信息，但保留其他状态
			setError(null);
		} else {
			// 如果当前没有文件或者不是闲置状态，则重置状态并设置新文件
			resetState();
			setFiles(droppedFiles);
		}
	};

	// 重置状态
	const resetState = () => {
		setError(null);
		setSuccess(false);
		setUploadProgress(0);
		setParseResults([]);
		setCurrentFileIndex(-1);
		setImportPhase("idle");
		setValidationError(null);
	};

	// 获取文件图标和描述
	const getFileTypeInfo = (fileName: string) => {
		const extension = fileName.split(".").pop()?.toLowerCase();
		if (extension === "dbf") {
			return {
				icon: <FileUpIcon className="size-4 text-blue-500" />,
				text: "DBF文件",
			};
		}
		// 对于非DBF文件，返回通用图标和文本
		return {
			icon: <FileTypeIcon className="size-4" />,
			text: "不支持的文件",
		};
	};
	// 清除选择的文件
	const clearFiles = useCallback(() => {
		setFiles([]);
		resetState();
	}, []);

	// 移除单个文件
	const removeFile = useCallback((index: number) => {
		setFiles((prev) => prev.filter((_, i) => i !== index));
	}, []);

	// 处理验证错误
	const handleValidationError = (
		message: string,
		fileIndex: number,
		results: ShareholderRegistryParseResult[] = [],
	) => {
		setValidationError({
			message,
			fileIndex,
			fileName: files[fileIndex]?.name,
		});
		setImportPhase("idle");

		// 存储当前的结果数组，以便后续继续处理
		if (results.length > 0) {
			setParseResults(results);
		}
	};

	// 处理跳过当前文件
	const handleSkipFile = useCallback(() => {
		if (validationError && validationError.fileIndex >= 0) {
			// 创建一个新的解析结果数组，将失败的文件标记为跳过
			const newResults = [...parseResults];
			newResults[validationError.fileIndex] = {
				success: false,
				fileName: files[validationError.fileIndex].name,
				error: {
					type: "FILE_ERROR",
					message: "已跳过此文件",
				},
			};

			// 更新UI
			setParseResults(newResults);

			// 清除错误并继续下一个文件
			setValidationError(null);

			// 继续处理下一个文件
			validateAndImportNext(validationError.fileIndex + 1, newResults);
		}
	}, [validationError, parseResults, files]);

	// 模拟上传进度
	const simulateProgressUpdate = (callback: (progress: number) => void) => {
		// 清除之前的定时器
		if (progressTimerRef.current) {
			clearInterval(progressTimerRef.current);
		}

		// 从0%开始
		let progress = 0;
		setUploadProgress(0);

		// 创建定时器，模拟进度更新
		progressTimerRef.current = setInterval(() => {
			// 根据当前进度动态调整增长速度，越接近90%增长越慢
			const increment = Math.max(0.5, 5 * (1 - progress / 90));
			progress += increment;

			// 确保进度不超过90%（最后10%在上传完成后设置）
			if (progress > 90) {
				progress = 90;
				// 达到90%时不停止定时器，保持在90%直到后端响应
				// 这样如果后端处理时间较长，用户能看到进度条停在90%
			}

			// 更新进度并回调
			setUploadProgress(Math.round(progress));
			callback(Math.round(progress));
		}, 200);
	};

	// 在组件卸载时清除定时器
	useEffect(() => {
		return () => {
			if (progressTimerRef.current) {
				clearInterval(progressTimerRef.current);
			}
		};
	}, []);

	// 验证并导入下一个文件
	const validateAndImportNext = async (
		nextIndex: number,
		currentResults: ShareholderRegistryParseResult[] = [],
	) => {
		if (nextIndex >= files.length) {
			// 所有文件处理完成
			setImportPhase("idle");

			// 使用传入的currentResults数组，而不是依赖parseResults状态
			const successCount = currentResults.filter(
				(r) => r?.success === true,
			).length;
			const totalRecords = currentResults.reduce((total, result) => {
				return (
					total + (result?.success ? (result?.recordCount ?? 0) : 0)
				);
			}, 0);

			// 一次性设置最终的解析结果
			setParseResults(currentResults);
			setSuccess(true);
			setCurrentFileIndex(-1);

			// 只显示一次汇总成功信息，仅当成功数大于0时
			if (successCount > 0) {
				// 如果有多个文件，可以提供批量导入的汇总信息
				if (files.length > 1) {
					toast.success("批量导入完成", {
						description: `成功导入 ${successCount}/${files.length} 个文件，共 ${totalRecords} 条记录`,
					});
				}
			}

			// 立即调用onComplete函数，不再延迟
			if (onComplete) {
				onComplete();
			}
			return;
		}

		setCurrentFileIndex(nextIndex);
		setImportPhase("validating");

		try {
			// 解析文件
			const result = await parseShareholderRegistryFile(files[nextIndex]);
			// 创建新的结果数组，而不是修改parseResults状态
			const newResults = [...currentResults];
			while (newResults.length <= nextIndex) {
				newResults.push({ success: false, fileName: "" });
			}

			newResults[nextIndex] = {
				...result,
				success: result.success,
			};

			// 更新UI显示，但不依赖这个状态进行后续处理
			setParseResults(newResults);

			// 检查解析是否成功
			if (!result.success) {
				// 显示验证错误
				handleValidationError(
					result.error?.message || "文件验证失败",
					nextIndex,
					newResults,
				);
				return;
			}

			// 验证成功，开始导入
			setImportPhase("importing");
			setUploadProgress(0);
			
			// 在调用uploadRegistry之前就开始模拟进度条
			simulateProgressUpdate((_progress) => {
				// 进度回调，可以在这里添加额外逻辑
			});

			// 上传到服务器
			try {
				await uploadRegistry({
					fileName: result.fileName,
					recordCount: result.recordCount || 0,
					registerDate: result.registerDate || "",
					companyCode: result.companyCode || "",
					companyInfo: result.companyInfo || {
						companyName: "",
						totalShares: "0",
						totalShareholders: 0,
						totalInstitutions: 0,
						largeSharesCount: "0",
						largeShareholdersCount: 0,
						institutionShares: "0",
					},
					shareholders:
						result.records?.map((record) => ({
							shareholderId: record.ZJDM || record.XYZHZJDM || "",
							unifiedAccountNumber: record.YMTH || "",
							securitiesAccountName:
								(record.ZQZHMC || record.XYZHMC || "")?.replace(/#/g, ""),
							shareholderCategory: record.CYRLBMS || "",
							numberOfShares: String(record.CGSL || "0"),
							lockedUpShares: String(record.XSGSL || "0"),
							shareholdingRatio: String(record.CGBL || "0"),
							frozenShares: String(record.DJGS || "0"),
							cashAccount: record.PTZQZH,
							sharesInCashAccount: String(record.PTZHCGSL || "0"),
							marginAccount: record.XYZQZH,
							sharesInMarginAccount: String(
								record.XYZHCGSL || 
								(record.XYZQZH && !record.XYZHCGSL ? record.CGSL : "0")
							),
							contactAddress: record.TXDZ,
							contactNumber: record.DHHM,
							zipCode: record.YZBM,
							relatedPartyIndicator: record.GLGXBS,
							clientCategory: record.KHLB,
							remarks: record.BZ,
							marginCollateralAccountNumber: record.HZZQZH,
							marginCollateralAccountName: record.HZZHMC,
							natureOfShares: record.GFXZ,
						})) || [],
				});

				// 清除进度模拟定时器
				if (progressTimerRef.current) {
					clearInterval(progressTimerRef.current);
					progressTimerRef.current = null;
				}

				// 设置进度为100%，模拟从当前进度到100%的平滑过渡
				const finalizeProgress = () => {
					// 从当前进度（应该是90%左右）开始
					let currentProgress = uploadProgress;
					const progressInterval = setInterval(() => {
						currentProgress += 2;
						if (currentProgress >= 100) {
							currentProgress = 100;
							clearInterval(progressInterval);
						}
						setUploadProgress(currentProgress);
					}, 50);
					
					// 确保在500毫秒后清除定时器（以防卡住）并设置为100%
					setTimeout(() => {
						clearInterval(progressInterval);
						setUploadProgress(100);
					}, 500);
				};
				
				finalizeProgress();

				// 标记当前文件为成功，但不依赖状态更新
				newResults[nextIndex] = {
					...result,
					success: true,
				};

				// 更新UI，但主要是为了显示
				setParseResults(newResults);

				// 继续处理下一个文件，传递当前的结果数组
				validateAndImportNext(nextIndex + 1, newResults);
			} catch (uploadError: any) {
				// 清除进度模拟定时器
				if (progressTimerRef.current) {
					clearInterval(progressTimerRef.current);
					progressTimerRef.current = null;
				}

				// 显示上传失败，进度回退到0
				const showFailure = () => {
					// 从当前进度逐渐减少到0，给用户一个视觉反馈
					let currentProgress = uploadProgress;
					const progressInterval = setInterval(() => {
						currentProgress -= 5;
						if (currentProgress <= 0) {
							currentProgress = 0;
							clearInterval(progressInterval);
						}
						setUploadProgress(currentProgress);
					}, 30);
					
					// 确保在300毫秒后清除定时器并设置为0
					setTimeout(() => {
						clearInterval(progressInterval);
						setUploadProgress(0);
					}, 300);
				};
				
				showFailure();

				// 显示上传错误
				handleValidationError(
					uploadError.message || "上传失败",
					nextIndex,
					newResults,
				);
			}
		} catch (err: any) {
			// 显示解析错误
			handleValidationError(
				err.message || "文件验证失败",
				nextIndex,
				currentResults,
			);
		}
	};

	// 处理文件上传
	const handleUpload = async () => {
		if (files.length === 0 || !organizationId) {
			setError("请先选择要上传的文件");
			return;
		}

		try {
			setError(null);
			setParseResults([]);
			setValidationError(null);

			// 开始验证第一个文件，传递空数组作为初始结果
			validateAndImportNext(0, []);
		} catch (err: any) {
			// 统一错误处理，将错误展示到界面上而不是打印到控制台
			setError(err?.message || "上传失败，请检查文件格式后重试");
			setImportPhase("idle");
		}
	};

	// 处理取消
	const handleCancel = useCallback(() => {
		clearFiles();
		if (onComplete) {
			onComplete();
		}
	}, [clearFiles, onComplete]);

	// 渲染组件
	return (
		// 主卡片容器
		<Card>
			{/* 卡片头部 */}
			<CardHeader>
				<div className="flex items-center gap-2">
					<FilesIcon className="text-primary h-5 w-5" />
					<CardTitle>导入股东名册</CardTitle>
				</div>
				<CardDescription>
					上传股东名册文件，仅支持DBF格式，可同时上传多个文件
				</CardDescription>
			</CardHeader>

			{/* 卡片内容区域 */}
			<CardContent>
				{/* 错误提示 */}
				{error && (
					<Alert className="border-destructive bg-destructive/5 text-destructive mb-4">
						<AlertCircleIcon className="h-4 w-4" />
						<AlertTitle>
							{error === "所选文件已在列表中"
								? "文件已存在"
								: "上传失败"}
						</AlertTitle>
						<AlertDescription>{error}</AlertDescription>
					</Alert>
				)}

				{/* 成功提示 */}
				{success && (
					<Alert className="border-green-500 bg-green-500/5 text-green-500 mb-4">
						<CheckCircleIcon className="h-4 w-4" />
						<AlertTitle>导入成功</AlertTitle>
						<AlertDescription>
							成功处理{" "}
							{
								parseResults.filter((r) => r?.success === true)
									.length
							}
							/{files.length} 个文件
						</AlertDescription>
					</Alert>
				)}

				{/* 验证错误对话框 */}
				{validationError && (
					<ShareholderRegistryImportErrorDialog
						message={validationError.message}
						fileName={validationError.fileName}
						isLastFile={
							validationError.fileIndex === files.length - 1
						}
						onOpenHelpDialog={() => setShowHelpDialog(true)}
						onSkip={handleSkipFile}
						onCancel={handleCancel}
					/>
				)}

				{/* 文件上传区域 */}
				{!success && !validationError && (
					<div
						className={`rounded-lg border-2 border-dashed text-center transition-colors mb-4 p-6
                     border-muted-foreground/20 ${isDragging ? "border-primary bg-primary/5" : "hover:border-primary/50"}`}
						onDragOver={handleDragOver}
						onDragLeave={handleDragLeave}
						onDrop={handleDrop}
					>
						<UploadCloudIcon className="mx-auto text-muted-foreground mb-4 h-12 w-12" />
						<h3 className="mb-1 font-medium text-lg">
							拖拽文件到此处
						</h3>
						<p className="text-muted-foreground mb-4 text-sm">
							请导入从中国结算CSDC下载的原始股东名册文件
						</p>
						<p className="text-muted-foreground mb-4 text-sm">
							或点击选择文件（仅支持DBF格式，可选择多个文件）
						</p>
						<div className="text-center">
							<Label
								htmlFor="file-upload"
								className="inline-flex cursor-pointer items-center gap-1 rounded-md bg-primary font-medium text-primary-foreground px-4 py-2 text-sm"
							>
								<FileUpIcon className="h-4 w-4" />
								<span>选择文件</span>
							</Label>
							<Input
								id="file-upload"
								type="file"
								accept=".dbf"
								onChange={handleFileChange}
								className="hidden"
								multiple
							/>
						</div>
					</div>
				)}

				{/* 已选文件列表 */}
				{files.length > 0 && !success && !validationError && (
					<div className="space-y-2">
						{/* 文件列表头部 */}
						<div className="flex items-center justify-between mb-2">
							<div className="font-medium text-sm">
								已选择 {files.length} 个文件
							</div>
							<div className="flex items-center gap-2">
								{/* 导入按钮 */}
								{!success &&
									!validationError &&
									files.length > 0 && (
										<Button
											onClick={handleUpload}
											size="sm"
											disabled={
												files.length === 0 ||
												importPhase !== "idle"
											}
										>
											{importPhase !== "idle" ? (
												<>
													<Loader2Icon className="animate-spin mr-2 h-4 w-4" />
													处理中...
												</>
											) : (
												<>开始导入</>
											)}
										</Button>
									)}
								{/* 清除按钮 */}
								{files.length > 0 && (
									<Button
										variant="outline"
										size="sm"
										onClick={clearFiles}
										disabled={importPhase !== "idle"}
									>
										清除全部
									</Button>
								)}
							</div>
						</div>

						{/* 文件列表容器 */}
						<div className="srounded-md border-border bg-muted/30 p-2">
							<div className="max-h-[240px] overflow-y-auto pr-1 space-y-2">
								{files.length > 0 ? (
									// 遍历显示文件列表
									files.map((file, index) => (
										<div
											key={`${file.name}-${index}`}
											className="rounded-md bg-muted p-3 border border-border/30 hover:border-border/50 transition-colors"
										>
											{/* 文件信息行 */}
											<div className="flex items-center justify-between">
												<div className="flex items-center gap-2">
													{
														getFileTypeInfo(
															file.name,
														).icon
													}
													<div className="min-w-0 flex-1">
														<div className="font-medium truncate">
															{file.name}
														</div>
														<div className="text-muted-foreground text-xs">
															{
																getFileTypeInfo(
																	file.name,
																).text
															}{" "}
															•{" "}
															{(
																file.size /
																1024 /
																1024
															).toFixed(2)}{" "}
															MB
														</div>
													</div>
												</div>
												{/* 删除文件按钮 */}
												<Button
													variant="ghost"
													size="icon"
													className="h-8 w-8 flex-shrink-0"
													onClick={() =>
														removeFile(index)
													}
													disabled={
														importPhase !== "idle"
													}
												>
													<XIcon className="h-4 w-4" />
												</Button>
											</div>

											{/* 文件处理进度条 */}
											{currentFileIndex === index &&
												importPhase !== "idle" && (
													<div className="mt-2">
														<div className="mb-1 flex justify-between text-xs">
															<span>
																{importPhase ===
																"validating"
																	? "校验中..."
																	: "导入中..."}
															</span>
															{importPhase ===
																"importing" && (
																<span>
																	{
																		uploadProgress
																	}
																	%
																</span>
															)}
														</div>
														<Progress
															value={
																importPhase ===
																"validating"
																	? 0
																	: uploadProgress
															}
															className="h-2 w-full"
														/>
													</div>
												)}

											{/* 文件处理结果 */}
											{parseResults[index] && (
												<div className="mt-2 text-xs">
													{parseResults[index]
														.success ? (
														<span className="text-green-500">
															已成功处理，
															{
																parseResults[
																	index
																].recordCount
															}{" "}
															条记录
														</span>
													) : (
														<span className="text-destructive">
															{parseResults[index]
																.error
																?.message ||
																"处理失败"}
														</span>
													)}
												</div>
											)}
										</div>
									))
								) : (
									// 空状态提示
									<div className="text-center py-4 text-sm text-muted-foreground">
										暂无文件，请通过拖拽或点击上方按钮选择文件
									</div>
								)}
							</div>
						</div>

						{/* 总体导入进度 */}
						{importPhase !== "idle" && (
							<ShareholderRegistryImportProgress
								currentFileIndex={currentFileIndex}
								totalFiles={files.length}
								progress={uploadProgress}
								status={importPhase}
							/>
						)}
					</div>
				)}
			</CardContent>

			{/* 帮助对话框 */}
			<ShareholderRegistryHelpDialog
				isOpen={showHelpDialog}
				onOpenChange={setShowHelpDialog}
			/>
		</Card>
	);
} 