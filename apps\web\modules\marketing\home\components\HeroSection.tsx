
import { <PERSON><PERSON> } from "@ui/components/button";
import { ArrowRightIcon } from "lucide-react";
import Link from "next/link";
import SectionWrapper from "./SectionWrapper";

export function HeroSection() {
	return (
		<SectionWrapper
			className="min-h-screen flex items-center justify-center relative overflow-hidden pt-20 md:pt-24 bg-gradient-to-br from-stone-100 via-stone-50 to-indigo-100"
			containerClassName="text-center"
			style={{
				maskImage: 'linear-gradient(to bottom, black calc(100% - 200px), transparent 100%)',
				WebkitMaskImage: 'linear-gradient(to bottom, black calc(100% - 200px), transparent 100%)',
			}}
		>
			{/* Subtle animated blobs - conceptual, might need more for actual animation */}
			<div className="absolute inset-0 z-0 opacity-20">
				<div className="absolute top-1/4 left-1/4 w-96 h-96 bg-purple-400 rounded-full filter blur-3xl animate-pulse" />
				<div className="absolute bottom-1/4 right-1/4 w-96 h-96 bg-pink-400 rounded-full filter blur-3xl animate-pulse animation-delay-2000" />
				<div className="absolute top-1/2 left-1/3 w-80 h-80 bg-blue-400 rounded-full filter blur-3xl animate-pulse animation-delay-4000" />
			</div>

			<div className="relative z-10">
				<h1 className="text-4xl sm:text-5xl md:text-6xl lg:text-7xl font-extrabold tracking-tight text-transparent bg-clip-text bg-gradient-to-r from-neutral-900 via-neutral-800 to-neutral-700 mb-6 leading-tight">
					告别数据手动拼凑，
					<br className="hidden md:block" />
					AI Agent分钟级掌握<span className="text-transparent bg-clip-text bg-gradient-to-r from-purple-500 via-pink-500 to-orange-500">股东和市场变动</span>
				</h1>
				<p className="max-w-xl md:max-w-2xl mx-auto text-lg md:text-xl text-neutral-600 mb-10">
					基于 Data Agent 技术，为A股上市公司提供更专业的市值分析研判与决策支持，实现市值稳定和持续增长
				</p>
				<div className="flex flex-col sm:flex-row items-center justify-center space-y-4 sm:space-y-0 sm:space-x-4">
					<Button variant="primary" size="lg" asChild>
						<Link href="#">
							预约演示
							<ArrowRightIcon className="ml-2 size-4" />
						</Link>
					</Button>
					<Button variant="outline" size="lg" asChild>
						<Link href="#">
							开始使用
						</Link>
					</Button>
				</div>
			</div>
		</SectionWrapper>
	);
}
