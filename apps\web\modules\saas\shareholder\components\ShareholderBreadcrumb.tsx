"use client";

import { SharedBreadcrumb } from "@saas/shared/components/SharedBreadcrumb";
import type { BreadcrumbItem } from "@saas/shared/components/SharedBreadcrumb";
import { usePathname } from "next/navigation";
import { useMemo } from "react";

/**
 * 股东名册模块的面包屑导航组件
 * 
 * @param organizationSlug - 组织的 slug
 * @param currentPage - 可选的当前页面名称，如果不提供则根据路径自动确定
 * @returns 渲染的面包屑导航组件
 */
export function ShareholderBreadcrumb({ 
  organizationSlug, 
  currentPage: providedCurrentPage,
}: { 
  organizationSlug: string; 
  currentPage?: string;
}) {
  const pathname = usePathname(); // 获取当前路径
  
  // 解析路径，确定页面层次结构
  const pathSegments = useMemo(() => {
    // 从路径中提取股东名册模块之后的部分
    const shareholderIndex = pathname.indexOf('/shareholder');
    if (shareholderIndex === -1) {
      return [];
    }
    
    const shareholderPath = pathname.slice(shareholderIndex + '/shareholder'.length);
    // 分割路径部分，过滤掉空字符串
    return shareholderPath.split('/').filter(Boolean);
  }, [pathname]);
  
  // 获取页面名称映射
		const pageNameMap: Record<string, string> = {
			register: "股东数据",
			manage: "名册管理",
			analysis: "分析报告",
		};
  
  // 根据路径确定当前页面名称
  const currentPage = useMemo(() => {
    if (providedCurrentPage) {
      return providedCurrentPage;
    }
    
    // 如果有路径段，使用最后一个路径段确定当前页面
    if (pathSegments.length > 0) {
      const lastSegment = pathSegments[pathSegments.length - 1];
      return pageNameMap[lastSegment] || lastSegment;
    }
    
    return "股东名册";
  }, [pathSegments, providedCurrentPage, pageNameMap]);
  
  // 构建面包屑项目
  const breadcrumbItems: BreadcrumbItem[] = useMemo(() => {
    // 基础面包屑项目：应用 > 股东名册
    const items: BreadcrumbItem[] = [
      {
        label: "股东名册",
        href: `/app/${organizationSlug}/shareholder/register`
      }
    ];
    
    // 处理子路径的层级关系
    const basePath = `/app/${organizationSlug}/shareholder`;
    let currentPath = basePath;
    
    // 如果存在多级路径，逐级添加中间层级
    if (pathSegments.length > 1) {
      // 遍历除了最后一个路径段外的所有路径段
      for (let i = 0; i < pathSegments.length - 1; i++) {
        const segment = pathSegments[i];
        currentPath += `/${segment}`;
        
        // 添加中间层级到面包屑
        const segmentName = pageNameMap[segment] || segment;
        items.push({
          label: segmentName,
          href: currentPath
        });
      }
    }
    
    // 添加当前页面
    items.push({
      label: currentPage,
      isActive: true
    });
    
    return items;
  }, [organizationSlug, pathSegments, currentPage, pageNameMap]);
  
  
  return (
    <SharedBreadcrumb
      items={breadcrumbItems}
    />
  );
} 