
import React from 'react';

interface SectionWrapperProps {
  children: React.ReactNode;
  className?: string;
  id?: string;
  containerClassName?: string;
  style?: React.CSSProperties; // Added optional style prop
}

const SectionWrapper: React.FC<SectionWrapperProps> = ({ children, className = '', id, containerClassName = '', style }) => {
  return (
    <section id={id} className={`py-16 md:py-24 ${className}`} style={style}> {/* Apply the style prop here */}
      <div className={`container mx-auto px-4 sm:px-6 lg:px-8 max-w-7xl ${containerClassName}`}>
        {children}
      </div>
    </section>
  );
};

export default SectionWrapper;
