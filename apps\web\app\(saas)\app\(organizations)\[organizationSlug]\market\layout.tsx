import { getSession } from "@saas/auth/lib/server"; // 用于获取用户会话信息
import { MarketValueMenu } from "@saas/market/components/MarketValueMenu"; // 市值菜单组件
import type { marketValueMenuTranslationKey } from "@saas/market/components/MarketValueMenu"; // 市值菜单类型
import { HorizontalSidebarContentLayout } from "@saas/shared/components/HorizontalSidebarContentLayout"; // 水平侧边栏布局组件
import { MarketValueBreadcrumb } from "@saas/market/components/MarketValueBreadcrumb"; // 市值面包屑组件
import { getTranslations } from "next-intl/server"; // 国际化翻译功能
import { redirect } from "next/navigation"; // 页面重定向功能

// 定义路由参数类型
type Params = Promise<{
	organizationSlug: string; // 组织标识符
}>;

// 定义布局组件的Props接口
interface MarketValueLayoutProps {
	children: React.ReactNode; // 子组件
	params: Params; // 路由参数
}

/**
 * 市值管理模块的布局组件
 * 提供水平导航和内容区域的布局结构
 */
export default async function MarketValueLayout({ 
	children, 
	params 
}: MarketValueLayoutProps) {
	// 使用国际化翻译功能获取菜单翻译
	const t = await getTranslations("market-value.menu"); // 获取菜单翻译函数
	const session = await getSession(); // 获取用户会话信息

	// 如果用户未登录，重定向到登录页面
	if (!session) {
		return redirect("/auth/login");
	}
	
	// 解构获取组织标识符
	const { organizationSlug } = await params;
	
	// 使用简化的路径格式，但保持在组织slug下
	const basePath = `/app/${organizationSlug}/market`; // 在organizationSlug下简化路径

	// 定义菜单项配置
	const menuItems = [
		{
			// 不需要标题和头像，因为使用水平布局
			items: [
				{
					titleKey: "monitoring" as marketValueMenuTranslationKey, // 市值监控页面 - 使用国际化
					href: `${basePath}/monitoring`,
				},
				{
					titleKey: "industry-industry" as marketValueMenuTranslationKey, // 行业对标页面 - 使用国际化
					href: `${basePath}/industry`, // 简化为"industry"代表"industry-benchmark"
				},
				{
					titleKey: "analysis-report" as marketValueMenuTranslationKey, // 分析报告页面 - 使用国际化
					href: `${basePath}/report`, // 简化为"report"代表"analysis-report"
				},
			],
		},
	];

	// 渲染布局结构，使用水平布局
	return (
		<>
			{/* 添加顶部面包屑导航 */}
			<MarketValueBreadcrumb 
				organizationSlug={organizationSlug}
			/>
			<HorizontalSidebarContentLayout
				sidebar={<MarketValueMenu menuItems={menuItems} layout="horizontal" />} // 渲染水平菜单
			>
				{children} {/* 渲染子组件内容 */}
			</HorizontalSidebarContentLayout>
		</>
	);
} 