import type { WhyUsFeature } from "../types";

interface SectionWrapperProps {
	children: React.ReactNode;
	className?: string;
	id?: string;
	containerClassName?: string;
	style?: React.CSSProperties;
}

const SectionWrapper: React.FC<SectionWrapperProps> = ({
	children,
	className = '',
	id,
	containerClassName = '',
	style
}) => {
	return (
		<section id={id} className={`py-16 md:py-24 ${className}`} style={style}>
			<div className={`container mx-auto px-4 sm:px-6 lg:px-8 max-w-7xl ${containerClassName}`}>
				{children}
			</div>
		</section>
	);
};

interface FeatureCardProps {
	feature: WhyUsFeature;
}

const FeatureCard: React.FC<FeatureCardProps> = ({ feature }) => {
	const IconComponent = feature.icon;
	return (
		<div className="flex flex-col items-center text-center p-6 bg-white rounded-xl shadow-lg hover:shadow-purple-500/10 transition-all duration-300 border border-neutral-200/70 hover:border-purple-400/50 transform hover:-translate-y-1 h-full">
			<div className="mb-6 text-purple-500">
				<IconComponent className="w-20 h-20 md:w-24 md:h-24 text-purple-500 opacity-90" />
			</div>
			<h3 className="text-xl md:text-2xl font-semibold text-neutral-800 mb-3">{feature.title}</h3>
			<p className="text-neutral-600 text-sm md:text-base leading-relaxed">{feature.description}</p>
		</div>
	);
};

// Icons for "Why Us?" Section
const NetworkIcon: React.FC<{ className?: string }> = ({ className = "w-16 h-16 text-neutral-300" }) => (
	<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 64 64" strokeWidth="1.5" stroke="currentColor" className={className}>
		<title>Network Icon</title>
		<path strokeLinecap="round" strokeLinejoin="round" d="M32 54C44.1503 54 54 44.1503 54 32C54 19.8497 44.1503 10 32 10C19.8497 10 10 19.8497 10 32C10 44.1503 19.8497 54 32 54Z" strokeWidth="2"/>
		<path strokeLinecap="round" strokeLinejoin="round" d="M32 40C36.4183 40 40 36.4183 40 32C40 27.5817 36.4183 24 32 24C27.5817 24 24 27.5817 24 32C24 36.4183 27.5817 40 32 40Z" strokeWidth="1.5" fill="rgba(100,116,139,0.3)"/>
	</svg>
);

const HourglassIcon: React.FC<{ className?: string }> = ({ className = "w-16 h-16 text-neutral-300" }) => (
	<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 64 64" strokeWidth="1.5" stroke="currentColor" className={className}>
		<title>Hourglass Icon</title>
		<path strokeLinecap="round" strokeLinejoin="round" d="M14 12H50V20L36 32L50 44V52H14V44L28 32L14 20V12Z" strokeWidth="2" />
		<path strokeLinecap="round" strokeLinejoin="round" d="M18 18H46" strokeWidth="1.5" />
		<path strokeLinecap="round" strokeLinejoin="round" d="M22 26L32 32L42 26" strokeWidth="1.5" opacity="0.7" fill="rgba(100,116,139,0.3)" />
		<path strokeLinecap="round" strokeLinejoin="round" d="M18 46H46" strokeWidth="1.5" />
	</svg>
);

const LightbulbIcon: React.FC<{ className?: string }> = ({ className = "w-16 h-16 text-neutral-300" }) => (
	<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 64 64" strokeWidth="1.5" stroke="currentColor" className={className}>
		<title>Lightbulb Icon</title>
		<path strokeLinecap="round" strokeLinejoin="round" d="M32 8C24.268 8 18 14.268 18 22C18 26.418 20.105 30.365 23.5 32.5V42C23.5 43.933 25.067 45.5 27 45.5H37C38.933 45.5 40.5 43.933 40.5 42V32.5C43.895 30.365 46 26.418 46 22C46 14.268 39.732 8 32 8Z" strokeWidth="2"/>
		<path strokeLinecap="round" strokeLinejoin="round" d="M28.6289 28.7901L35.3711 27.2099L34.1165 22.8427L27.3742 24.4229L28.6289 28.7901Z" fill="rgba(200,200,100,0.3)"/>
	</svg>
);

const WHY_US_FEATURES: WhyUsFeature[] = [
	{
		id: 'data-insights',
		icon: NetworkIcon,
		title: '金融级数据安全',
		description: '数据加密 + 权限管控 = 股东名册绝不外泄',
	},
	{
		id: 'efficient-analysis',
		icon: HourglassIcon,
		title: '一站式AI分析',
		description: '股东变动、路演反馈、市场风险 —— AI提炼分析高价值信息，省掉80%无效阅读',
	},
	{
		id: 'proactive-strategy',
		icon: LightbulbIcon,
		title: '决策辅助而非替代',
		description: 'AI提供量化视角，结合董办专业判断，让人机协同的价值最大化',
	},
];

export function WhyUsSection() {
	return (
		<SectionWrapper className="bg-stone-100 dotted-bg">
			<div className="text-center mb-12 md:mb-16">
				<h2 className="text-3xl sm:text-4xl md:text-5xl font-bold text-neutral-900 mb-4">
					为什么选择<span className="text-transparent bg-clip-text bg-gradient-to-r from-purple-500 to-pink-500">我们?</span>
				</h2>
				<p className="max-w-2xl mx-auto text-neutral-600 text-lg">
					我们致力于提供尖端的AI解决方案，助您在复杂市场中运筹帷幄，决胜千里。
				</p>
			</div>
			<div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
				{WHY_US_FEATURES.map((feature) => (
					<FeatureCard key={feature.id} feature={feature} />
				))}
			</div>
		</SectionWrapper>
	);
}