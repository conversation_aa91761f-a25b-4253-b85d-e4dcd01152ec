import { getActiveOrganization } from "@saas/auth/lib/server";
import { getTranslations } from "next-intl/server";
import { notFound } from "next/navigation";
import { Suspense } from "react";
import dynamic from "next/dynamic";

// import OrganizationStart from "@saas/organizations/components/OrganizationStart";
// import { PageHeader } from "@saas/shared/components/PageHeader";

// 动态导入市值监控页面组件
const MarketValueMonitoringPage = dynamic(() => import("./market/monitoring/page"), {
	ssr: true,
});

// 动态导入市值布局组件
const MarketValueLayout = dynamic(() => import("./market/layout"), {
	ssr: true,
});

/**
 * 组织首页组件
 * @param params - 包含组织slug的路由参数对象
 * @returns 组织首页视图
 * 直接使用市值监控页面的内容，但保持URL在/app/[organizationSlug]
 */
export default async function OrganizationPage({
	params,
}: { params: Promise<{ organizationSlug: string }> }) {
	// 从路由参数中获取组织slug
	const { organizationSlug } = await params;
	// 获取国际化翻译函数
	const t = await getTranslations();

	// 根据slug获取当前活跃的组织信息
	const activeOrganization = await getActiveOrganization(
		organizationSlug as string,
	);

	// 如果找不到对应的组织,返回404页面
	if (!activeOrganization) {
		return notFound();
	}

	// 复用market-value布局和页面内容，而不是重定向
	// 使用Suspense包裹以提供加载状态
	return (
		<Suspense fallback={<div>加载中...</div>}>
			{/* 使用市值管理的布局组件 */}
			<MarketValueLayout params={params}>
				{/* 渲染市值监控页面组件 */}
				<MarketValueMonitoringPage params={params} />
			</MarketValueLayout>
		</Suspense>
	);

	// 原来方式
	// 	return (
	// 	<div>
	// 		{/* <PageHeader
	// 			title={activeOrganization.name}
	// 			subtitle={t("organizations.start.subtitle")}
	// 		/> */}

	// 		<OrganizationStart />
	// 	</div>
	// );
}
