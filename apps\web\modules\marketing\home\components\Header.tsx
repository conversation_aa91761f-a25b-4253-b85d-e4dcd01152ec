
"use client";

import { LocaleLink, useLocalePathname } from "@i18n/routing";
import { useSession } from "@saas/auth/hooks/use-session";
import { ColorModeToggle } from "@shared/components/ColorModeToggle";
import { LocaleSwitch } from "@shared/components/LocaleSwitch";
import { Button } from "@ui/components/button";
import { cn } from "@ui/lib";
import { MenuIcon, XIcon } from "lucide-react";

import NextLink from "next/link";
import Image from "next/image";
import { Suspense, useEffect, useState } from "react";
import { useDebounceCallback } from "usehooks-ts";

// 定义导航项类型
type NavItem = {
	name: string;
	href: string;
};

const navItems: NavItem[] = [
	{ name: "功能", href: "/#features" },
	{ name: "定价", href: "/#pricing" },
	{ name: "博客", href: "/blog" },
	{ name: "文档", href: "/docs" },
	{ name: "案例", href: "#" },
	{ name: "支持", href: "/contact" },
];

export function Header() {
	const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
	const [isTop, setIsTop] = useState(true);
	const localePathname = useLocalePathname();
	const session = useSession();

	const debouncedHandleScroll = useDebounceCallback(() => {
		setIsTop(window.scrollY < 10);
	}, 10);

	useEffect(() => {
		window.addEventListener("scroll", debouncedHandleScroll);
		return () => window.removeEventListener("scroll", debouncedHandleScroll);
	}, [debouncedHandleScroll]);

	const isDocsPage = localePathname.startsWith("/docs");

	const isMenuItemActive = (href: string) => localePathname.startsWith(href);

	return (
		<header
			className={cn(
				"fixed top-0 left-0 z-50 w-full transition-shadow duration-200",
				!isTop || isDocsPage
					? "bg-card/80 shadow-sm backdrop-blur-lg"
					: "shadow-none",
			)}
		>
			<div className="container mx-auto px-4 sm:px-6 lg:px-8">
				<div className="flex items-center justify-between h-16">
					{/* Logo */}
					<div className="flex-shrink-0">
						<LocaleLink href="/" className="flex items-center space-x-2">
							<Image
								src="/logo.png"
								width={80}
								height={80}
								alt="Logo"
								className="object-contain"
								priority
							/>
						</LocaleLink>
					</div>

					{/* Desktop Navigation */}
					<div className="hidden md:flex md:items-center md:space-x-6 lg:space-x-8">
						{navItems.map((item) => (
							<LocaleLink
								key={item.name}
								href={item.href}
								className={cn(
									"px-3 py-2 rounded-md text-sm font-medium transition-colors",
									isMenuItemActive(item.href)
										? "text-primary"
										: "text-foreground/60 hover:text-primary",
								)}
							>
								{item.name}
							</LocaleLink>
						))}
					</div>

					{/* Desktop Actions */}
					<div className="hidden md:flex md:items-center md:space-x-4">
						<Suspense fallback={null}>
							<LocaleSwitch />
						</Suspense>
						<ColorModeToggle />
						{session ? (
							<Button variant="outline" size="sm" asChild>
								<NextLink href="/app">控制台</NextLink>
							</Button>
						) : (
							<Button variant="outline" size="sm" asChild>
								<NextLink href="/auth/login">登录</NextLink>
							</Button>
						)}
					</div>

					{/* Mobile Menu Button */}
					<div className="md:hidden flex items-center">
						<button
							type="button"
							onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
							className="inline-flex items-center justify-center p-2 rounded-md text-foreground/60 hover:text-foreground hover:bg-secondary/10 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-primary"
							aria-expanded={isMobileMenuOpen}
						>
							<span className="sr-only">Open main menu</span>
							{isMobileMenuOpen ? (
								<XIcon className="block h-6 w-6" />
							) : (
								<MenuIcon className="block h-6 w-6" />
							)}
						</button>
					</div>
				</div>
			</div>

			{/* Mobile Menu */}
			{isMobileMenuOpen && (
				<div className="md:hidden bg-card/95 backdrop-blur-lg border-t">
					<div className="px-2 pt-2 pb-3 space-y-1 sm:px-3">
						{navItems.map((item) => (
							<LocaleLink
								key={item.name}
								href={item.href}
								className={cn(
									"block px-3 py-2 rounded-md text-base font-medium transition-colors",
									isMenuItemActive(item.href)
										? "text-primary bg-primary/10"
										: "text-foreground/60 hover:text-primary hover:bg-secondary/10",
								)}
								onClick={() => setIsMobileMenuOpen(false)}
							>
								{item.name}
							</LocaleLink>
						))}
					</div>
					<div className="pt-4 pb-3 border-t border-border">
						<div className="px-5 space-y-3">
							<div className="flex items-center justify-between">
								<Suspense fallback={null}>
									<LocaleSwitch />
								</Suspense>
								<ColorModeToggle />
							</div>
							{session ? (
								<Button variant="outline" size="sm" className="w-full" asChild>
									<NextLink href="/app">控制台</NextLink>
								</Button>
							) : (
								<Button variant="outline" size="sm" className="w-full" asChild>
									<NextLink href="/auth/login">登录</NextLink>
								</Button>
							)}
						</div>
					</div>
				</div>
			)}
		</header>
	);
}
