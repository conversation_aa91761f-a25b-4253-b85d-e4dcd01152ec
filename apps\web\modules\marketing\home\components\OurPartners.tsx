import React from 'react';
import SectionWrapper from './SectionWrapper';
import { PARTNERS_DATA, GalaxyBackground } from './constants';
import type { Partner } from '../types';

interface PartnerCardProps {
  partner: Partner;
}

const PartnerCard: React.FC<PartnerCardProps> = ({ partner }) => {
  return (
    <div className="bg-white p-6 rounded-xl shadow-lg hover:shadow-purple-500/10 transition-all duration-300 border border-neutral-200/70 hover:border-purple-400/50 transform hover:-translate-y-1 flex items-center justify-center h-32">
      {/* In a real scenario, you'd use an <img> tag or an SVG component for the logo */}
      {/* <img src={partner.logoUrl} alt={`${partner.name} Logo`} className="max-h-16 w-auto object-contain" /> */}
      <span className="text-xl font-semibold text-neutral-700 group-hover:text-purple-600 transition-colors text-center">
        {partner.name}
      </span>
    </div>
  );
};

export function OurPartners() {
  return (
    <SectionWrapper className="bg-stone-100 relative overflow-hidden"> {/* Added relative and overflow-hidden */}
      <GalaxyBackground />
      <div className="relative z-10"> {/* Content container */}
        <div className="text-center mb-12 md:mb-16">
          <h2 className="text-3xl sm:text-4xl md:text-5xl font-bold text-neutral-900 mb-4">
            我们的<span className="text-transparent bg-clip-text bg-gradient-to-r from-purple-500 to-pink-500">合作伙伴</span>
          </h2>
          <p className="max-w-2xl mx-auto text-neutral-600 text-lg">
            携手业界领先企业，共同推动技术创新与发展。
          </p>
        </div>

        <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-4 gap-6 md:gap-8">
          {PARTNERS_DATA.map((partner) => (
            <PartnerCard key={partner.id} partner={partner} />
          ))}
        </div>
      </div>
    </SectionWrapper>
  );
};

export default OurPartners;
