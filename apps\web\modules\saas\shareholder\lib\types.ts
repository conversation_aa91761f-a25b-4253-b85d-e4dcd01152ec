/**
 * 股东数据相关类型定义
 */

// 股东项类型
export interface ShareholderItem {
	id: string;
	shareholderId: string;
	unifiedAccountNumber: string;
	securitiesAccountName: string;
	shareholderCategory: string;
	numberOfShares: string;
	lockedUpShares: string;
	shareholdingRatio: string;
	frozenShares: string;
	contactAddress?: string;
	contactNumber?: string;
	zipCode?: string;
	cashAccount?: string;
	sharesInCashAccount?: string;
	marginAccount?: string;
	sharesInMarginAccount?: string;
	relatedPartyIndicator?: string;
	clientCategory?: string;
	remarks?: string;
	marginCollateralAccountNumber?: string;
	marginCollateralAccountName?: string;
	natureOfShares?: string;

	// 新增字段
	shareholderTags?: string[]; // 股东标签
	rankInPeriod?: number; // 当期排名
	change?: {
		// 变动信息
		type:
			| "increase"
			| "decrease"
			| "new"
			| "exit"
			| "unchanged"
			| "modified";
		field?: string; // 变动的字段
		oldValue?: string; // 变动前的值
		newValue?: string; // 变动后的值
	};
}

// 股东列表响应类型
export interface ShareholdersResponse {
  shareholders: ShareholderItem[];
  pagination: PaginationInfo;
}

// 分页信息
export interface PaginationInfo {
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}

// 期数日期项类型
export interface RegisterDateItem {
  registerDate: string;
  companyCode: string;
}

// 名册上传相关类型
export interface ShareholderRegistryUploadResult {
  id: string;
  fileName: string;
  recordCount: number;
  registerDate: string;
  uploadedAt: string;
  success?: boolean;
  error?: {
    code: string;
    message: string;
    details?: any;
  };
}

// 名册上传响应扩展
export interface ShareholderRegistryUploadResponse {
  success: boolean;
  id?: string;
  fileName?: string;
  recordCount?: number;
  registerDate?: string;
  uploadedAt?: string;
  code?: number;
  error?: {
    code: string;
    message: string;
    details?: any;
  };
}

// 名册列表项
export interface ShareholderRegistryItem {
  id: string;
  fileName: string;
  recordCount: number;
  registerDate: string;
  companyCode: string;
  companyName: string;
  uploadedAt: string;
  companyDetail?: {
    companyName: string;
    totalShares: string;
    totalShareholders: number;
    totalInstitutions: number;
    largeSharesCount: string;
    institutionShares: string;
    largeShareholdersCount: number;
    // 控股股东信息
    controllingShareholderInfo?: {
      securitiesAccountName: string;  // 证券账户名称
      shareholdingRatio: string;      // 持股比例
      numberOfShares: string;         // 持股数量
    };
    // 前十股东持股信息
    topTenShareholdersInfo?: {
      totalRatio: string;            // 前十大股东持股比例总和
      totalShares: string;           // 前十大股东持股数量总和
    };
  };
  userName?: string;
}

// 名册列表响应
export interface ShareholderRegistryListResponse {
  registries: ShareholderRegistryItem[];
  pagination: PaginationInfo;
}

// 排序方向类型
export type SortOrder = 'asc' | 'desc';

// 股东查询参数
export interface ShareholderQueryParams {
  registerDate?: string;
  page?: number;
  limit?: number;
  searchTerm?: string;
  sortBy?: string;
  sortOrder?: SortOrder;
} 