import { useState, useEffect, useRef, useCallback } from "react";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { shareholderApi } from "@saas/shareholder/lib/api";
import { shareholderRegistryApi } from "@saas/shareholder/lib/registry-api";
import { toast } from "sonner";
import type {
	ShareholdersResponse,
	RegisterDateItem,
	ShareholderRegistryUploadResult,
	ShareholderRegistryListResponse,
	SortOrder,
} from "@saas/shareholder/lib/types";

/**
 * 获取股东列表钩子
 * @param organizationId 组织ID
 * @param initialRegisterDate 初始股东名册日期（YYYY-MM-DD格式）
 * @returns 股东列表查询结果和控制函数
 */
export function useShareholders(organizationId: string, initialRegisterDate?: string) {
  const [page, setPage] = useState(1);
  const [limit, setLimit] = useState(20);
  const [searchTerm, setSearchTerm] = useState<string | undefined>(undefined);
  const [sortBy, setSortBy] = useState<string>("numberOfShares");
  const [sortOrder, setSortOrder] = useState<SortOrder>('desc');
  const [registerDate, setRegisterDate] = useState<string | undefined>(initialRegisterDate);
  const [registerDates, setRegisterDates] = useState<RegisterDateItem[]>([]);
  const [isLoadingRegisterDates, setIsLoadingRegisterDates] = useState(false);
  
  // 使用 useRef 跟踪是否已经设置过初始日期，避免重复设置
  const hasSetInitialDate = useRef(false);
  // 使用 useRef 跟踪是否已经加载过日期列表，避免重复加载
  const hasLoadedDates = useRef(false);
  // 使用 useRef 跟踪是否应该查询股东列表
  const shouldQueryShareholders = useRef(false);
  // 使用 queryClient 来手动控制缓存
  const queryClient = useQueryClient();
  
  // 增强版setLimit函数，改变每页条数时重置页码
  const handleLimitChange = (newLimit: number) => {
    setLimit(newLimit);
    setPage(1); // 重置到第一页，确保数据正确加载
  };
  
  // 获取报告期日期列表
  const registerDatesQuery = useQuery({
    queryKey: ["shareholder-register-dates", organizationId],
    queryFn: async () => {
      if (!organizationId) {
        return { registerDates: [] };
      }
      
      setIsLoadingRegisterDates(true);
      try {
        const result = await shareholderRegistryApi.getRegisterDates(organizationId);
        return result;
      } catch (error) {
        toast.error("获取股东名册日期列表失败");
        return { registerDates: [] };
      } finally {
        setIsLoadingRegisterDates(false);
      }
    },
    // 修改查询启用条件，不再使用hasLoadedDates.current作为控制条件
    // 只要组织ID存在就可以启用查询
    enabled: !!organizationId,
    // 减少缓存时间，使得数据更容易刷新
    staleTime: 30 * 1000, // 30秒内不重新获取
  });
  
  // 手动刷新期数日期列表
  const refreshRegisterDates = useCallback(() => {
    // 重置标记，允许重新加载日期列表
    hasLoadedDates.current = false;
    // 使缓存失效，强制重新获取数据
    queryClient.invalidateQueries({ 
      queryKey: ["shareholder-register-dates", organizationId] 
    });
  }, [organizationId, queryClient]);
  
  // 当日期列表加载完成后，处理日期数据和设置初始日期
  useEffect(() => {
    if (registerDatesQuery.data) {
      // 不再检查hasLoadedDates.current，每次数据更新都处理
      const { registerDates: dates = [] } = registerDatesQuery.data;
      
      // 处理日期数据
      if (Array.isArray(dates)) {
        // 处理每个元素，确保类型安全
        const datesArray = dates
          .map((item: {registerDate?: string, companyCode?: string}) => ({
            registerDate: String(item.registerDate || ""),
            companyCode: String(item.companyCode || ""),
          }))
          .filter((item: RegisterDateItem) => !!item.registerDate);
        
        setRegisterDates(datesArray);
        
        // 如果有新的日期数据，且当前没有设置registerDate或者之前的registerDate不在新的日期列表中
        // 则设置为最新日期
        if (datesArray.length > 0) {
          const currentDateExists = registerDate && 
            datesArray.some(item => item.registerDate === registerDate);
          
          if (!registerDate || !currentDateExists) {
            // 设置为最新日期
            setRegisterDate(datesArray[0].registerDate);
            hasSetInitialDate.current = true;
          }
        } else if (registerDate) {
          // 如果没有日期数据但当前有设置日期，清除日期设置
          setRegisterDate(undefined);
        }
        
        // 标记已加载日期列表
        hasLoadedDates.current = true;
      }
    }
  }, [registerDatesQuery.data, registerDate]);
  
  // 查询股东列表
  const query = useQuery<ShareholdersResponse>({
    queryKey: ["shareholders", organizationId, registerDate, page, limit, searchTerm, sortBy, sortOrder],
    queryFn: async () => {
      // 转换排序字段，将前端的'rankInPeriod'转换为后端API的'rank'
      const apiSortBy = sortBy === 'rankInPeriod' ? 'rank' : sortBy;
      
      const result = await shareholderApi.getShareholderList(organizationId, {
        registerDate,
        page,
        limit,
        searchTerm,
        sortBy: apiSortBy, // 使用转换后的排序字段
        sortOrder,
      });
      
      // 检查返回的数据结构是否符合预期
      if (result && typeof result === 'object') {
        if (!result.shareholders) {
          // 创建一个带有空shareholders数组的响应对象
          return { 
            shareholders: [], 
            pagination: result.pagination || { 
              total: 0, 
              page: page, 
              limit: limit, 
              totalPages: 0 
            } 
          } as ShareholdersResponse;
        }
        
        if (!Array.isArray(result.shareholders)) {
          return { 
            shareholders: [], 
            pagination: result.pagination || { 
              total: 0, 
              page: page, 
              limit: limit, 
              totalPages: 0 
            } 
          } as ShareholdersResponse;
        }
        
        // 处理后端返回的rank字段，将其映射为rankInPeriod
        const processedShareholders = result.shareholders.map(shareholder => ({
          ...shareholder,
          // 如果rank存在，则赋值给rankInPeriod
          rankInPeriod: shareholder.rank || shareholder.rankInPeriod
        }));
        
        return { 
          shareholders: processedShareholders, 
          pagination: result.pagination 
        } as ShareholdersResponse;
      }
      
      // 默认情况下返回空数据
      return { 
        shareholders: [], 
        pagination: { 
          total: 0, 
          page: page, 
          limit: limit, 
          totalPages: 0 
        } 
      } as ShareholdersResponse;
    },
    // 只有当组织ID和registerDate都存在时才启用查询
    enabled: !!organizationId && !!registerDate,
    // 设置合理的缓存时间，避免频繁请求
    staleTime: 30 * 1000, // 30秒内不重新获取
  });

  // 当手动设置registerDate时，确保可以查询股东列表
  useEffect(() => {
    if (registerDate && !shouldQueryShareholders.current) {
      shouldQueryShareholders.current = true;
    }
  }, [registerDate]);

  // 重置搜索条件
  const resetSearch = () => {
    setSearchTerm(undefined);
    setPage(1);
  };
  
  // 处理报告日期选择
  const handleRegisterDateChange = (date: string) => {
    setRegisterDate(date);
    setPage(1);
  };
  
  return {
    // 数据和状态
    shareholders: query.data?.shareholders || [],
    pagination: query.data?.pagination,
    isLoading: query.isLoading,
    error: query.error,
    
    // 期数相关
    registerDate,
    registerDates,
    isLoadingRegisterDates,
    
    // 分页和筛选控制
    page,
    limit,
    searchTerm,
    sortBy,
    sortOrder,
    
    // 设置函数
    setPage,
    setLimit: handleLimitChange,
    setSearchTerm,
    setSortBy,
    setSortOrder,
    setRegisterDate: handleRegisterDateChange,
    
    // 功能函数
    resetSearch,
    
    // 刷新数据
    refetch: query.refetch,
    // 添加刷新期数日期列表的方法
    refreshRegisterDates
  };
}

/**
 * 使用股东名册钩子
 * @param organizationId 组织ID
 */
export function useShareholderRegistry(organizationId: string) {
  const queryClient = useQueryClient();
  const [page, setPage] = useState(1);
  const [limit, setLimit] = useState(20);
  const [companyCode, setCompanyCode] = useState<string | undefined>(undefined);

  // 增强版setLimit函数，改变每页条数时重置页码
  const handleLimitChange = (newLimit: number) => {
    setLimit(newLimit);
    setPage(1); // 重置到第一页，确保数据正确加载
  };

  // 获取股东名册列表
  const registryListQuery = useQuery<ShareholderRegistryListResponse>({
    queryKey: ['shareholder-registry-list', organizationId, page, limit, companyCode],
    queryFn: async () => {
      try {
        const result = await shareholderRegistryApi.getShareholderRegistryList(
          organizationId, 
          { page, limit, companyCode }
        );
        // 处理可能的响应格式问题
        if (!result) {
          // 如果没有结果，返回空数据
          return {
            registries: [],
            pagination: {
              total: 0,
              page: page,
              limit: limit,
              totalPages: 0
            }
          };
        }
     
        // 确保结果是正确的类型
        if (typeof result === 'object') {
          // 如果已经处理过解密，是正确格式就直接返回
          if (result.registries && Array.isArray(result.registries)) {
            return result as ShareholderRegistryListResponse;
          }
          // 如果数据结构不完整，创建默认结构
          return {
            registries: result.registries || [],
            pagination: result.pagination || {
              total: 0,
              page: page,
              limit: limit,
              totalPages: 0
            }
          };
        }
        
        // 默认情况下返回空数据
        return {
          registries: [],
          pagination: {
            total: 0,
            page: page,
            limit: limit,
            totalPages: 0
          }
        };
      } catch (error) {
        // 出错时返回空数据
        return {
          registries: [],
          pagination: {
            total: 0,
            page: page,
            limit: limit,
            totalPages: 0
          }
        };
      }
    },
    enabled: !!organizationId,
  });

  // 获取期数日期列表
  const registerDatesQuery = useQuery<{ registerDates: RegisterDateItem[] }>({
    queryKey: ['shareholder-registry-register-dates', organizationId, companyCode],
    queryFn: async () => {
      const result = await shareholderRegistryApi.getRegisterDates(organizationId, companyCode);
      return result as unknown as { registerDates: RegisterDateItem[] };
    },
    enabled: !!organizationId,
    // 减少缓存时间，使得数据更容易刷新
    staleTime: 30 * 1000, // 30秒内不重新获取
  });

  // 上传股东名册
  const uploadMutation = useMutation<
    ShareholderRegistryUploadResult,
    Error,
    Parameters<typeof shareholderRegistryApi.uploadShareholderRegistry>[1]
  >({
    mutationFn: async (data) => {
      const result = await shareholderRegistryApi.uploadShareholderRegistry(organizationId, data);
      
      // 检查上传结果
      if (!result.success) {
        // 如果上传失败，抛出错误以便onError处理器接收
        throw new Error(
          result.error?.message || '上传股东名册失败，请重试'
        );
      }
      
      // 成功情况下返回结果
      return result as unknown as ShareholderRegistryUploadResult;
    },
    onSuccess: (data) => {
      // 使所有相关查询缓存失效，确保数据刷新
      queryClient.invalidateQueries({ 
        queryKey: ['shareholder-registry-list', organizationId] 
      });
      queryClient.invalidateQueries({ 
        queryKey: ['shareholder-registry-register-dates', organizationId] 
      });
      queryClient.invalidateQueries({
        queryKey: ['shareholders', organizationId]
      });
      queryClient.invalidateQueries({
        queryKey: ['shareholder-register-dates', organizationId]
      });
      return data;
    },
    onError: (error: any) => { 
      // 显示更友好的错误消息
      toast.error('上传失败', {
        description: error.message || '请检查文件格式后重试'
      });
    },
  });

  // 删除股东名册
  const deleteMutation = useMutation<
    any,
    Error,
    { registryId: string }
  >({
    mutationFn: async ({ registryId }) => {
      const result = await shareholderRegistryApi.deleteShareholderRegistry(registryId);
      
      // 检查删除结果
      if (!result.success) {
        // 如果删除失败，抛出错误以便onError处理器接收
        throw new Error(
          result.error?.message || '删除股东名册失败，请重试'
        );
      }
      
      return result;
    },
    onSuccess: () => {
      // 使所有相关查询缓存失效，确保数据刷新
      queryClient.invalidateQueries({ 
        queryKey: ['shareholder-registry-list', organizationId] 
      });
      queryClient.invalidateQueries({ 
        queryKey: ['shareholder-registry-register-dates', organizationId] 
      });
      queryClient.invalidateQueries({
        queryKey: ['shareholders', organizationId]
      });
      queryClient.invalidateQueries({
        queryKey: ['shareholder-register-dates', organizationId]
      });
    },
    onError: (error: any) => {
      // 显示用户友好的错误消息，不在控制台打印错误
      toast.error(
        error.message || '删除股东名册失败，请重试'
      );
    },
  });

  return {
    // 股东名册列表数据
    registries: registryListQuery.data?.registries || [],
    registriesPagination: registryListQuery.data?.pagination,
    isLoadingRegistries: registryListQuery.isLoading,
    registriesError: registryListQuery.error,
    
    // 报告日期数据
    registerDates: registerDatesQuery.data?.registerDates || [],
    isLoadingRegisterDates: registerDatesQuery.isLoading,
    registerDatesError: registerDatesQuery.error,
    
    // 分页和过滤控制
    page,
    limit,
    companyCode,
    setPage,
    setLimit: handleLimitChange,
    setCompanyCode,
    
    // 变更操作
    uploadRegistry: uploadMutation.mutateAsync,
    deleteRegistry: deleteMutation.mutateAsync,
    isUploading: uploadMutation.isPending,
    isDeleting: deleteMutation.isPending,
    
    // 刷新数据
    refetchRegistries: registryListQuery.refetch,
    refetchRegisterDates: registerDatesQuery.refetch
  };
} 