// 导入必要的依赖
import { getSession } from "@saas/auth/lib/server"; // 用于获取用户会话信息
import { ShareholderMenu } from "@saas/shareholder/components/ShareholderMenu"; // 股东菜单组件
import type { shareholderMenuTranslationKey } from "@saas/shareholder/components/ShareholderMenu"; // 股东菜单类型
import { HorizontalSidebarContentLayout } from "@saas/shared/components/HorizontalSidebarContentLayout"; // 水平侧边栏布局组件
import { ShareholderBreadcrumb } from "@saas/shareholder/components/ShareholderBreadcrumb"; // 股东名册面包屑组件
import { getTranslations } from "next-intl/server"; // 国际化翻译功能
import { redirect } from "next/navigation"; // 页面重定向功能

// 定义路由参数类型
type Params = Promise<{
	organizationSlug: string; // 组织标识符
}>;

// 定义布局组件的Props接口
interface ShareholderLayoutProps {
	children: React.ReactNode; // 子组件
	params: Params; // 路由参数
}

/**
 * 股东管理模块的布局组件
 * 提供水平导航和内容区域的布局结构
 */
export default async function ShareholderLayout({ 
	children, 
	params 
}: ShareholderLayoutProps) {
	const t = await getTranslations("shareholder.menu"); // 获取菜单翻译函数
	const session = await getSession(); // 获取用户会话信息

	// 如果用户未登录，重定向到登录页面
	if (!session) {
		return redirect("/auth/login");
	}
	
	// 解构获取组织标识符
	const { organizationSlug } = await params;
	const basePath = `/app/${organizationSlug}/shareholder`; // 构建基础路径

	// 定义菜单项配置
	const menuItems = [
		{
			// 不需要标题和头像，因为使用水平布局
			items: [
				{
					titleKey: "register" as shareholderMenuTranslationKey, // 名册页面
					href: `${basePath}/register`, // 名册页面路径
				},
				{
					titleKey: "roster-manage" as shareholderMenuTranslationKey, // 导入页面
					href: `${basePath}/manage`, // 导入页面路径
				},
				{
					titleKey: "analysis" as shareholderMenuTranslationKey, // 分析页面
					href: `${basePath}/analysis`, // 分析页面路径
				}
			],
		},
	];

	// 渲染布局结构，使用水平布局
	return (
		<>
			{/* 添加顶部面包屑导航 */}
			<ShareholderBreadcrumb 
				organizationSlug={organizationSlug}
			/>
			<HorizontalSidebarContentLayout
				sidebar={<ShareholderMenu menuItems={menuItems} layout="horizontal" />} // 渲染水平菜单
			>
				{children} {/* 渲染子组件内容 */}
			</HorizontalSidebarContentLayout>
		</>
	);
} 