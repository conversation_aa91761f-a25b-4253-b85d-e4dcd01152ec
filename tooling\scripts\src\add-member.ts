import { auth } from "@repo/auth";
import { db } from "@repo/database";
import { logger } from "@repo/logs";
import { nanoid } from "nanoid";

/**
 * 根据邮箱或ID查找用户
 * @param identifier 用户邮箱或ID
 * @returns 用户对象或null
 */
async function findUserByIdentifier(identifier: string) {
	// 首先尝试通过邮箱查找
	let user = await db.user.findUnique({
		where: {
			email: identifier,
		},
	});

	// 如果通过邮箱没找到，尝试通过ID查找
	if (!user) {
		user = await db.user.findUnique({
			where: {
				id: identifier,
			},
		});
	}

	return user;
}

/**
 * 根据名称或ID查找组织
 * @param identifier 组织名称、slug或ID
 * @returns 组织对象或null
 */
async function findOrganizationByIdentifier(identifier: string) {
	// 首先尝试通过ID查找
	let organization = await db.organization.findUnique({
		where: {
			id: identifier,
		},
	});

	// 如果通过ID没找到，尝试通过slug查找
	if (!organization) {
		organization = await db.organization.findUnique({
			where: {
				slug: identifier,
			},
		});
	}

	// 如果通过slug没找到，尝试通过名称查找
	if (!organization) {
		organization = await db.organization.findFirst({
			where: {
				name: {
					equals: identifier,
					mode: "insensitive",
				},
			},
		});
	}

	return organization;
}

/**
 * 创建新用户
 * @param email 用户邮箱
 * @param name 用户姓名
 * @returns 创建的用户对象
 */
async function createUser(email: string, name: string) {
	const authContext = await auth.$context;
	const userPassword = nanoid(16);
	const hashedPassword = await authContext.password.hash(userPassword);
	const userId = nanoid();
	const accountId = nanoid();
	const now = new Date();

	// 使用事务创建用户和账户
	const result = await db.$transaction(async (tx) => {
		// 创建用户
		const user = await tx.user.create({
			data: {
				id: userId,
				email,
				name,
				role: "user",
				emailVerified: true,
				createdAt: now,
				updatedAt: now,
				onboardingComplete: true,
			},
		});

		// 创建认证账户
		await tx.account.create({
			data: {
				id: accountId,
				userId: user.id,
				accountId: user.id,
				providerId: "credential",
				createdAt: now,
				updatedAt: now,
				password: hashedPassword,
			},
		});

		return { user, password: userPassword };
	});

	return result;
}

/**
 * 检查用户是否已经是组织成员
 * @param userId 用户ID
 * @param organizationId 组织ID
 * @returns 是否已是成员
 */
async function isUserMember(userId: string, organizationId: string) {
	const member = await db.member.findUnique({
		where: {
			userId_organizationId: {
				userId,
				organizationId,
			},
		},
	});

	return !!member;
}

/**
 * 添加用户到组织
 * @param userId 用户ID
 * @param organizationId 组织ID
 * @param role 用户角色
 * @returns 创建的成员记录
 */
async function addUserToOrganization(
	userId: string,
	organizationId: string,
	role: string,
) {
	const memberId = nanoid();
	const now = new Date();

	const member = await db.member.create({
		data: {
			id: memberId,
			organizationId,
			userId,
			role,
			createdAt: now,
		},
		include: {
			user: {
				select: {
					name: true,
					email: true,
				},
			},
			organization: {
				select: {
					name: true,
					slug: true,
				},
			},
		},
	});

	return member;
}

async function main() {
	logger.info("👥 让我们为组织添加新成员！");

	// 获取组织标识
	const organizationIdentifier = await logger.prompt(
		"请输入组织名称、slug或ID:",
		{
			required: true,
			placeholder: "我的公司 或 my-company 或 org_123",
			type: "text",
		},
	);

	// 查找组织
	logger.info("正在查找组织...");
	const organization = await findOrganizationByIdentifier(
		organizationIdentifier,
	);

	if (!organization) {
		logger.error(`❌ 组织 ${organizationIdentifier} 不存在`);
		return;
	}

	logger.info(`✅ 找到组织: ${organization.name} (${organization.slug})`);

	// 获取用户邮箱
	const email = await logger.prompt("请输入用户邮箱:", {
		required: true,
		placeholder: "<EMAIL>",
		type: "text",
	});

	// 验证邮箱格式
	const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
	if (!emailRegex.test(email)) {
		logger.error("❌ 邮箱格式不正确");
		return;
	}

	// 获取用户角色
	const roleChoice = await logger.prompt("请选择用户角色 (1-成员, 2-管理员, 3-所有者):", {
		required: true,
		placeholder: "1",
		type: "text",
	});

	let role: string;
	switch (roleChoice.trim()) {
		case "1":
		case "member":
		case "成员":
			role = "member";
			break;
		case "2":
		case "admin":
		case "管理员":
			role = "admin";
			break;
		case "3":
		case "owner":
		case "所有者":
			role = "owner";
			break;
		default:
			logger.error("❌ 无效的角色选择，默认使用 member");
			role = "member";
			break;
	}

	try {
		// 查找用户
		logger.info("正在查找用户...");
		let user = await findUserByIdentifier(email);
		let isNewUser = false;
		let userPassword: string | undefined;

		if (!user) {
			// 用户不存在，询问是否创建
			const shouldCreateUser = await logger.prompt(
				`用户 ${email} 不存在，是否创建新用户？`,
				{
					required: true,
					type: "confirm",
					default: true,
				},
			);

			if (!shouldCreateUser) {
				logger.info("操作已取消");
				return;
			}

			// 获取用户姓名
			const name = await logger.prompt("请输入用户姓名:", {
				required: true,
				placeholder: "张三",
				type: "text",
			});

			logger.info("正在创建新用户...");
			const createResult = await createUser(email, name);
			user = createResult.user;
			userPassword = createResult.password;
			isNewUser = true;

			logger.success(`✅ 用户创建成功: ${user.name} (${user.email})`);
			if (userPassword) {
				logger.info(`🔑 用户密码: ${userPassword}`);
			}
		} else {
			logger.info(`✅ 找到用户: ${user.name} (${user.email})`);
		}

		// 检查用户是否已经是组织成员
		const isMember = await isUserMember(user.id, organization.id);
		if (isMember) {
			logger.error(`❌ 用户 ${user.email} 已经是组织 ${organization.name} 的成员`);
			return;
		}

		// 添加用户到组织
		logger.info("正在添加用户到组织...");
		const member = await addUserToOrganization(user.id, organization.id, role);

		logger.success("✅ 成员添加成功！");
		logger.info("📋 成员信息:");
		logger.info(`   用户: ${member.user.name} (${member.user.email})`);
		logger.info(`   组织: ${member.organization.name} (${member.organization.slug})`);
		logger.info(`   角色: ${member.role}`);
		logger.info(`   成员ID: ${member.id}`);
		logger.info(`   添加时间: ${member.createdAt.toISOString()}`);

		if (isNewUser && userPassword) {
			logger.info("");
			logger.info("🔐 重要提醒:");
			logger.info(`   新用户密码: ${userPassword}`);
			logger.info("   请将此密码安全地提供给用户");
		}
	} catch (error) {
		logger.error(
			`❌ 添加成员失败: ${error instanceof Error ? error.message : String(error)}`,
		);
	}
}

main().catch((error) => {
	logger.error("脚本执行失败:", error);
	process.exit(1);
});
