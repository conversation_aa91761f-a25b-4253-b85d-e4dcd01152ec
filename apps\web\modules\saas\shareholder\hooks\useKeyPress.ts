"use client";

import { useEffect, useCallback, DependencyList } from "react";

/**
 * 键盘按键钩子
 * @param targetKey 目标按键
 * @param callback 按键触发的回调函数
 * @param deps 依赖数组
 */
export function useKeyPress(
  targetKey: string,
  callback: () => void,
  deps: DependencyList = []
) {
  const handler = useCallback(
    (event: KeyboardEvent) => {
      if (event.key === targetKey) {
        callback();
      }
    },
    [targetKey, callback, ...deps]
  );

  useEffect(() => {
    window.addEventListener("keydown", handler);
    return () => {
      window.removeEventListener("keydown", handler);
    };
  }, [handler]);
} 