import { Prisma } from "@prisma/client";
import type { Context } from "hono";
import { HTTPException } from "hono/http-exception";




/**
 * 处理股东字段更新逻辑
 * 
 * 功能说明:
 * - 根据名册类型(01或05)处理不同字段的更新
 * - 仅当现有记录中字段为空且新数据有值时才更新
 * - 支持01和05名册的特有字段和共有字段的更新
 * 
 * 更新规则:
 * 1. 05名册特有字段:
 *    - marginCollateralAccountNumber (保证金账户号码)
 *    - marginCollateralAccountName (保证金账户名称)
 *    - natureOfShares (股份性质)
 * 
 * 2. 01名册特有字段:
 *    - contactNumber (联系电话)
 *    - zipCode (邮政编码)
 *    - relatedPartyIndicator (关联方标识)
 *    - clientCategory (客户类别)
 *    - remarks (备注)
 * 
 * 3. 共有字段:
 *    - contactAddress (联系地址)
 *    - cashAccount (现金账户)
 *    - sharesInCashAccount (现金账户股数)
 *    - marginAccount (信用账户)
 *    - sharesInMarginAccount (信用账户股数)
 * 
 * @param updateData - 用于存储需要更新的字段和值的对象
 * @param existingShareholder - 数据库中现有的股东记录
 * @param newShareholder - 新的股东数据
 * @param registryType - 名册类型，可选值: "01" | "05"
 * @returns 布尔值，表示是否有字段需要更新
 */
export function processShareholderUpdateFields(
  updateData: Record<string, unknown>,
  existingShareholder: any,
  newShareholder: any,
  registryType: "01" | "05"
): boolean {
  let hasUpdates = false;
  
  // 处理05名册特有字段
  if (registryType === "05") {
    if (!existingShareholder.marginCollateralAccountNumber && newShareholder.marginCollateralAccountNumber) {
      updateData.marginCollateralAccountNumber = newShareholder.marginCollateralAccountNumber;
      hasUpdates = true;
    }
    
    if (!existingShareholder.marginCollateralAccountName && newShareholder.marginCollateralAccountName) {
      updateData.marginCollateralAccountName = newShareholder.marginCollateralAccountName;
      hasUpdates = true;
    }
    
    if (!existingShareholder.natureOfShares && newShareholder.natureOfShares) {
      updateData.natureOfShares = newShareholder.natureOfShares;
      hasUpdates = true;
    }
  }
  
  // 处理01名册特有字段
  if (registryType === "01") {
    if (!existingShareholder.contactNumber && newShareholder.contactNumber) {
      updateData.contactNumber = newShareholder.contactNumber;
      hasUpdates = true;
    }
    
    if (!existingShareholder.zipCode && newShareholder.zipCode) {
      updateData.zipCode = newShareholder.zipCode;
      hasUpdates = true;
    }
    
    if (!existingShareholder.relatedPartyIndicator && newShareholder.relatedPartyIndicator) {
      updateData.relatedPartyIndicator = newShareholder.relatedPartyIndicator;
      hasUpdates = true;
    }
    
    if (!existingShareholder.clientCategory && newShareholder.clientCategory) {
      updateData.clientCategory = newShareholder.clientCategory;
      hasUpdates = true;
    }
    
    if (!existingShareholder.remarks && newShareholder.remarks) {
      updateData.remarks = newShareholder.remarks;
      hasUpdates = true;
    }
  }
  
  // 处理共有字段，仅当现有值为空时更新
  if (!existingShareholder.contactAddress && newShareholder.contactAddress) {
    updateData.contactAddress = newShareholder.contactAddress;
    hasUpdates = true;
  }
  
  if (!existingShareholder.cashAccount && newShareholder.cashAccount) {
    updateData.cashAccount = newShareholder.cashAccount;
    hasUpdates = true;
  }
  
  if (!existingShareholder.sharesInCashAccount && newShareholder.sharesInCashAccount) {
    updateData.sharesInCashAccount = newShareholder.sharesInCashAccount;
    hasUpdates = true;
  }
  
  if (!existingShareholder.marginAccount && newShareholder.marginAccount) {
    updateData.marginAccount = newShareholder.marginAccount;
    hasUpdates = true;
  }
  
  if (!existingShareholder.sharesInMarginAccount && newShareholder.sharesInMarginAccount) {
    updateData.sharesInMarginAccount = newShareholder.sharesInMarginAccount;
    hasUpdates = true;
  }
  
  return hasUpdates;
} 

/**
 * 处理日期转换的工具函数
 * 将字符串日期格式转换为日期对象
 * 
 * @param dateString 日期字符串，格式为YYYY-MM-DD
 * @returns 日期对象
 */
export function parseRegisterDate(dateString: string): Date {
	// 验证日期格式
	if (!/^\d{4}-\d{2}-\d{2}$/.test(dateString)) {
		throw new Error(`无效的日期格式'${dateString}'，应为YYYY-MM-DD`);
	}
	return new Date(dateString);
}

/**
 * 将日期对象转换为YYYY-MM-DD格式的字符串
 * 
 * @param date 日期对象
 * @returns 格式化的日期字符串
 */
export function formatRegisterDate(date: Date): string {
	return date.toISOString().split("T")[0];
}

/**
 * 构建分页信息
 * 
 * @param total 总记录数
 * @param page 当前页码
 * @param limit 每页条数
 * @returns 分页信息对象
 */
export function buildPagination(total: number, page: number, limit: number) {
  const totalPages = Math.ceil(total / limit);
  return {
    total,
    page,
    limit,
    totalPages
  };
}

/**
 * 构建排序参数
 * 
 * @param sortBy 排序字段
 * @param sortOrder 排序方向，"asc"或"desc"
 * @returns Prisma排序参数对象
 */
export function buildOrderBy(sortBy: string, sortOrder: "asc" | "desc"): Prisma.ShareholderOrderByWithRelationInput {
  return {
    [sortBy]: sortOrder
  };
}

/**
 * 构建搜索条件
 * 
 * @param searchTerm 搜索关键词
 * @returns Prisma搜索条件对象
 */
export function buildSearchCondition(searchTerm?: string) {
  if (!searchTerm) {
    return {};
  }
  
  return {
    OR: [
      { securitiesAccountName: { contains: searchTerm } },
      { shareholderId: { contains: searchTerm } },
      { unifiedAccountNumber: { contains: searchTerm } },
      { contactNumber: { contains: searchTerm } }
    ]
  };
}

/**
 * 处理特殊字符串转换为十进制数
 * 主要用于处理DBF文件中的数值型字段
 * 
 * @param value 字符串数值
 * @returns Prisma.Decimal 对象
 */
export function toDecimal(value: string): Prisma.Decimal {
  try {
    return new Prisma.Decimal(value.trim().replace(/,/g, ''));
  } catch (error) {
    console.error(`无法转换为数值: ${value}`, error);
    return new Prisma.Decimal(0);
  }
}

/**
 * 错误码类型定义
 */
export type ErrorCode = 
  | "UNAUTHORIZED" 
  | "FORBIDDEN" 
  | "RESOURCE_NOT_FOUND" 
  | "VALIDATION_ERROR" 
  | "DUPLICATE_REGISTRY" 
  | "DUPLICATE_REGISTRY_TYPE" 
  | "COMPLETE_REGISTRY" 
  | "COMPANY_NAME_MISMATCH" 
  | "COMPANY_CODE_MISMATCH"
  | "RECORD_COUNT_MISMATCH"
  | "DATABASE_ERROR" 
  | "INTERNAL_SERVER_ERROR";

/**
 * 统一响应格式接口
 */
export interface ApiResponse<T = any> {
  code: number;
  message: string;
  data: T | null;
}

/**
 * 错误响应格式接口
 */
export interface ApiErrorResponse {
  code: number;
  message: string;
  error?: {
    code: ErrorCode;
    message: string;
  };
  data: null;
}

/**
 * 成功响应处理函数
 * 
 * @param c Hono上下文
 * @param message 成功提示信息
 * @param data 响应数据
 * @param code 状态码，默认200
 */
export function successResponse<T>(
  c: Context,
  message: string,
  data: T,
  code = 200
): void {
  const response: ApiResponse<T> = {
    code,
    message,
    data
  };
  
  c.set("response", response);
}

/**
 * 错误响应处理函数
 * 
 * @param c Hono上下文
 * @param message 错误提示信息
 * @param code HTTP状态码
 * @param errorCode 错误码
 * @param errorMessage 详细错误信息
 */
export function errorResponse(
  c: Context,
  message: string,
  code = 500,
  errorCode?: ErrorCode,
  errorMessage?: string
): void {
  const response: ApiErrorResponse = {
    code,
    message,
    data: null
  };
  
  if (errorCode) {
    response.error = {
      code: errorCode,
      message: errorMessage || message
    };
  }
  
  c.set("response", response);
}

/**
 * HTTP异常处理函数
 * 将HTTPException转换为标准错误响应
 * 
 * @param c Hono上下文
 * @param error 错误对象
 */
export function handleHttpException(c: Context, error: unknown): void {
  // P2002 是 Prisma 唯一约束错误代码
  const isPrismaUniqueConstraintError = 
    error instanceof Error && 
    (error.message.includes('Unique constraint failed') || 
    (error as any)?.code === 'P2002');

  // 对于唯一约束错误，提供友好的错误信息而不在控制台输出完整错误
  if (isPrismaUniqueConstraintError) {
    errorResponse(
      c, 
      "该组织在此报告日期已经上传过该公司的股东名册，请先删除原有名册后再上传", 
      409, 
      "DUPLICATE_REGISTRY",
      "同一组织在同一日期只能有一份相同公司代码的股东名册。请前往名册管理页面删除已有名册，然后再尝试上传。"
    );
    return;
  }
  
  // 对其他错误类型保留完整日志
  console.error("[股东名册] 错误:", error);
  
  if (error instanceof HTTPException) {
    // 从错误消息中解析错误类型
    const errorMessage = error.message;
    
    // 处理名册类型重复上传的错误
    if (errorMessage.startsWith("DUPLICATE_REGISTRY_TYPE:")) {
      const userMessage = errorMessage.split(":")[1];
      errorResponse(
        c, 
        userMessage, 
        error.status, 
        "DUPLICATE_REGISTRY_TYPE",
        "您已经上传过相同类型的名册，不能重复上传。请先删除原有名册后再上传。"
      );
      return;
    }
    
    // 处理已完成01和05名册合并的错误
    if (errorMessage.startsWith("COMPLETE_REGISTRY:")) {
      const userMessage = errorMessage.split(":")[1];
      errorResponse(
        c, 
        userMessage, 
        error.status, 
        "COMPLETE_REGISTRY",
        "该股东名册已经完成01和05类型的合并，不能继续上传。如需修改，请先删除原有名册后再上传。"
      );
      return;
    }
    
    errorResponse(c, errorMessage, error.status);
    return;
  }
  
  if (error instanceof Error) {
    errorResponse(c, error.message || "处理请求时发生错误", 500);
    return;
  }
  
  // 默认错误处理
  errorResponse(c, "处理请求时发生未知错误", 500, "INTERNAL_SERVER_ERROR");
} 