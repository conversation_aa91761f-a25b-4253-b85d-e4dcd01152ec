/**
 * DBF文件解析和验证工具
 * 用于处理股东名册DBF文件的解析和验证
 * 支持01和05名册的解析和合并
 *
 * 主要功能:
 * 1. 根据文件名和内容判断名册类型(01或05)
 * 2. 调用相应的解析器处理不同类型的名册
 * 3. 支持01和05名册的字段映射和数据合并
 * 4. 提供统一的解析结果格式
 *
 * 使用场景:
 * - 用于解析证券公司提供的股东名册DBF文件
 * - 支持处理包含股东信息、持股数量等数据的DBF文件
 * - 支持01和05名册的合并处理
 */

// 导入通用解析工具和类型
import type { ShareholderRegistryParseResult } from "./dbf-parser-common";
import { 
	detectRegistryType,
	isProcessableFileType,
	isSupportedFileType
} from "./dbf-parser-common";

// 导入01名册解析器
import { parseDBFFile01 } from "./dbf-parser-01";

// 导入05名册解析器
import { parseDBFFile05 } from "./dbf-parser-05";

// 重新导出类型和工具函数，保持向后兼容
export type { ShareholderRegistryParseResult };
export {
	isSupportedFileType,
	isProcessableFileType,
};

/**
 * 解析DBF文件
 * 根据文件类型(01或05)调用相应的解析器处理
 *
 * @param file DBF文件对象
 * @returns 解析结果
 */
export async function parseDBFFile(
	file: File,
): Promise<ShareholderRegistryParseResult> {
	try {
		// 判断文件类型(01或05)
		const registryType = detectRegistryType(file.name);

		// 根据文件类型调用相应的解析器
		if (registryType === "01") {
			return await parseDBFFile01(file);
		}

		if (registryType === "05") {
			return await parseDBFFile05(file);
		}

		// 如果无法确定类型，尝试使用01名册解析器解析
		// 因为01名册更常见，且字段更全面
		const result = await parseDBFFile01(file);

		// 如果01名册解析失败，尝试使用05名册解析器
		if (!result.success) {
			const result05 = await parseDBFFile05(file);
			if (result05.success) {
				return result05;
			}
		}

		return result;
	} catch (error) {
		return {
			success: false,
			fileName: file.name,
			error: {
				type: "FILE_ERROR",
				message:
					error instanceof Error ? error.message : "解析DBF文件失败",
			},
		};
	}
}

/**
 * 解析文件名
 * 从文件名中提取公司代码和报告日期信息
 * 保持向后兼容性
 *
 * @param fileName 文件名
 * @returns 解析结果对象，包含公司代码和报告日期
 */
export function parseFileName(fileName: string): {
	companyCode?: string;
	registerDate?: string;
} {
	try {
		// 文件名示例: DQMC01_001339_20240930.DBF 或 DQMC05_001339_20240930.DBF
		const nameParts = fileName.split("_");

		if (nameParts.length >= 3) {
			// 提取公司代码，通常是文件名中的第二部分
			const companyCode = nameParts[1]; // 例如：001339

			// 提取日期部分，通常是文件名中的第三部分（去掉扩展名）
			let dateStr = nameParts[2]; // 例如：20240930.DBF
			// 移除可能的文件扩展名
			dateStr = dateStr.split(".")[0]; // 例如：20240930

			// 尝试从不同格式中解析日期
			let year: string;
			let month: string;
			let day: string;

			// 标准8位格式 YYYYMMDD
			if (dateStr.length === 8) {
				year = dateStr.substring(0, 4); // 2024
				month = dateStr.substring(4, 6); // 09
				day = dateStr.substring(6, 8); // 30
			}
			// 处理异常格式，例如9位或其他格式
			else if (dateStr.length > 8) {
				// 尝试仍然提取前8位作为YYYYMMDD
				const firstEight = dateStr.substring(0, 8);
				year = firstEight.substring(0, 4); // 2024
				month = firstEight.substring(4, 6); // 09
				day = firstEight.substring(6, 8); // 30
			} else {
				return { companyCode };
			}

			// 验证提取的日期是否有效
			const date = new Date(Number(year), Number(month) - 1, Number(day));
			if (
				date.getFullYear() !== Number(year) ||
				date.getMonth() !== Number(month) - 1 ||
				date.getDate() !== Number(day)
			) {
				// 日期无效
				return { companyCode };
			}

			// 转换为YYYY-MM-DD格式，与DBF文件中DHHM字段的格式一致
			const registerDate = `${year}-${month}-${day}`;

			return { companyCode, registerDate };
		}

		// 文件名格式不符合预期
		return {};
	} catch (error) {
		// 解析文件名失败
		return {};
	}
}
