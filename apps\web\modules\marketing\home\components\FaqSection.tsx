"use client";
import React, { useState } from 'react';
import SectionWrapper from './SectionWrapper';
import type { FAQItem } from '../types';
import { ChevronDownIcon } from './constants';

const faqData: FAQItem[] = [
  {
    id: 'faq1',
    question: '我们的股东名册数据是高度机密的，上传到平台安全吗？',
    answer: '数据安全是星链AI市值管理平台 服务的核心基石。我们围绕「传输 - 存储 - 权限 - 合规」构建全流程保障体系：传输与存储环节采用金融级加密机制守护数据安全，部署合规数据中心实现信息托管；权限管理支持董办自主定义数据访问范围，仅授权人员可接触核心信息；同时严格遵循《数据安全法》《个人信息保护法》等法规，承诺数据仅用于为您提供专属服务，从技术到制度全方位筑牢安全防线。'
  },
  {
    id: 'faq2',
    question: '股东分析的数据来源有哪些？是否权威可靠？',
    answer: '星链AI市值管理平台 的股东分析数据整合 “权威公开端 + 企业内部端” 双维度资源。公开端对接交易所披露平台、合规行情终端等资本市场核心数据渠道，确保信息源头可追溯、更新及时；企业端则基于您上传的股东名册、历史股权变动记录等内部资料，定向生成专属分析结论，双维度协同为决策提供扎实数据支撑。'
  },
  {
    id: 'faq3',
    question: 'AI 输出的分析结论，能直接作为决策依据吗？',
    answer: '星链市值管理平台输出的分析结论是「决策辅助载体」，旨在为董办提供「数据化视角」缩小决策盲区。我们通过量化呈现「股东变动趋势、市场反馈优先级、舆情风险等级」等维度，为决策提供客观参考；但最终决策需结合公司战略方向、行业动态、管理层判断等多重因素综合考量 —— 这也正是 AI 工具与董办专业判断力互补协作的价值体现，让科学决策更具整体性与前瞻性。'
  },
];

interface FAQAccordionItemProps {
  item: FAQItem;
  isOpen: boolean;
  onToggle: () => void;
}

const FAQAccordionItem: React.FC<FAQAccordionItemProps> = ({ item, isOpen, onToggle }) => {
  return (
    <div className="border-b border-neutral-200/70"> {/* Changed border color */}
      <h3>
        <button
          type="button"
          aria-expanded={isOpen}
          aria-controls={`faq-answer-${item.id}`}
          onClick={onToggle}
          className="flex justify-between items-center w-full py-5 md:py-6 text-left text-neutral-700 hover:text-purple-600 focus:outline-none focus-visible:ring-2 focus-visible:ring-purple-500 focus-visible:ring-offset-2 focus-visible:ring-offset-stone-50 rounded-sm group transition-colors" // Text, hover, focus offset for stone-50
        >
          <span className="text-lg md:text-xl font-medium group-hover:text-purple-500 transition-colors">{item.question}</span> {/* Hover color */}
          <ChevronDownIcon
            className={`w-6 h-6 text-neutral-500 group-hover:text-purple-500 transition-transform duration-300 transform ${ // Icon color
              isOpen ? 'rotate-180' : 'rotate-0'
            }`}
          />
        </button>
      </h3>
      <section
        id={`faq-answer-${item.id}`}
        // role="region" // Added role="region"
        aria-labelledby={`faq-question-${item.id}`}
        className={`overflow-hidden transition-all duration-300 ease-in-out ${
          isOpen ? 'max-h-screen opacity-100 pb-5' : 'max-h-0 opacity-0'
        }`}
      >
        <p className="text-neutral-600 text-sm md:text-base leading-relaxed"> {/* Text color */}
          {item.answer}
        </p>
      </section>
    </div>
  );
};

export function FAQSection() {
  const [openItemId, setOpenItemId] = useState<string | null>(null);

  const handleToggle = (itemId: string) => {
    setOpenItemId(prevId => (prevId === itemId ? null : itemId));
  };

  return (
    <SectionWrapper className="bg-stone-50 dotted-bg"> {/* Changed background to bg-stone-50 */}
      <div className="md:flex md:gap-x-12 lg:gap-x-16 xl:gap-x-20 items-start">
        <div className="md:w-2/5 lg:w-1/3 text-left mb-10 md:mb-0 md:sticky md:top-24">
          <h2 className="text-3xl sm:text-4xl md:text-5xl font-bold text-neutral-900 mb-4"> {/* Changed text color */}
            常见问题<span className="text-transparent bg-clip-text bg-gradient-to-r from-purple-500 to-pink-500">解答</span> {/* Adjusted gradient */}
          </h2>
          <p className="text-neutral-600 text-lg"> {/* Changed text color */}
            想了解星链AI? 您所关心的技术等常见问题, 答案都在这里.
          </p>
        </div>

        <div className="md:w-3/5 lg:w-2/3">
          {faqData.map((item) => (
            <FAQAccordionItem
              key={item.id}
              item={item}
              isOpen={openItemId === item.id}
              onToggle={() => handleToggle(item.id)}
            />
          ))}
        </div>
      </div>
    </SectionWrapper>
  );
};

