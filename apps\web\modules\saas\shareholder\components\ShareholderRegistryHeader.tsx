"use client";

import { Button } from "@ui/components/button";
import { useState, useEffect, useCallback } from "react";
import { Trash2, UploadIcon, Loader2, X } from "lucide-react";
import { useSystemScale } from "@saas/shareholder/hooks/useSystemScale";
import { cn } from "@ui/lib";

interface ShareholderRegistryHeaderProps {
	onImport?: () => void;
	onBatchDelete?: () => void;
	onSearch?: (term: string) => void;
	companyCode?: string;
	companyName?: string; // 添加公司简称参数
	isLoading?: boolean; // 添加加载状态
	selectedCount?: number; // 添加选中数量
	onPeriodRefresh?: () => void; // 添加刷新期数的回调函数
}

/**
 * 股东名册管理页面的头部组件
 * 根据原型图重新设计，包含标题、搜索框和操作按钮
 * 
 * @version 3.8.0 (2025-05-28) - 修改批量删除按钮为纯图标按钮，初始状态为X图标，确认状态为垃圾桶图标
 * @version 3.7.0 (2025-05-27) - 添加公司简称显示
 * @version 3.6.0 (2025-05-27) - 添加期数刷新机制，确保删除名册后期数信息更新
 * @version 3.5.0 (2025-05-24) - 增大名册导入按钮的图标尺寸，提高可见性
 * @version 3.4.0 (2025-05-23) - 统一边框大小和高度，与ShareholderFilter组件保持一致
 * @version 3.3.0 (2025-05-23) - 统一边框圆角样式，与ShareholderFilter组件保持一致
 * @version 3.2.0 (2025-05-20) - 增加批量删除API支持，新增加载状态和选中数量指示
 * @version 3.1.0 (2025-05-19) - 增加系统缩放响应支持，优化高缩放比例下的显示效果
 * @version 3.0.0 (2025-05-19) - 增加响应式布局，支持不同分辨率设备
 * @version 2.0.0 (2025-05-19) - 移除移动端适配逻辑，只保留桌面端布局
 */
export function ShareholderRegistryHeader({
	onImport,
	onBatchDelete,
	companyCode = "-", // 默认值
	companyName = "-", // 默认值
	isLoading = false, // 默认非加载状态
	selectedCount = 0, // 默认选中0项
	onPeriodRefresh, // 期数刷新回调
}: ShareholderRegistryHeaderProps) {
	// 使用系统缩放hook获取缩放比例和样式配置
	const { scale, styles, formStyles } = useSystemScale();
	// 批量删除确认状态
	const [batchDeleteConfirm, setBatchDeleteConfirm] = useState(false);
	// 批量删除确认定时器
	const [batchDeleteTimer, setBatchDeleteTimer] = useState<NodeJS.Timeout | null>(null);

	
	// 处理批量删除点击 - 使用useCallback优化性能
	const handleBatchDeleteClick = useCallback(() => {
		// 如果正在加载中，不响应点击
		if (isLoading) {
			return;
		}
		
		if (batchDeleteConfirm) {
			// 如果是二次确认，执行删除操作
			if (onBatchDelete) {
				onBatchDelete();
				// 删除操作后触发期数刷新
				if (onPeriodRefresh) {
					setTimeout(() => {
						onPeriodRefresh();
					}, 500); // 延迟执行，确保删除操作完成
				}
			}
			// 清除确认状态
			setBatchDeleteConfirm(false);
			// 清除定时器
			if (batchDeleteTimer) {
				clearTimeout(batchDeleteTimer);
				setBatchDeleteTimer(null);
			}
		} else {
			// 设置确认状态
			setBatchDeleteConfirm(true);
			// 设置3秒后自动取消确认状态的定时器
			const timerId = setTimeout(() => {
				setBatchDeleteConfirm(false);
			}, 3000);
			setBatchDeleteTimer(timerId);
		}
	}, [batchDeleteConfirm, batchDeleteTimer, onBatchDelete, isLoading, onPeriodRefresh]);
	
	// 组件卸载时清除定时器
	useEffect(() => {
		return () => {
			if (batchDeleteTimer) {
				clearTimeout(batchDeleteTimer);
			}
		};
	}, [batchDeleteTimer]);

	// 响应式布局
	return (
		<div className="w-full">
			{/* 操作区域 - 使用flex-wrap使元素在窄屏下能够换行 */}
			<div
				className={cn(
					"flex flex-col sm:flex-row justify-between items-start sm:items-center w-full gap-4 sm:gap-0",
					scale > 1.25 ? "mb-3" : "mb-4",
				)}
			>
				{/* 信息区域 - 小屏幕下垂直排列 */}
				<div className="flex flex-col sm:flex-row items-start sm:items-center gap-3 sm:gap-4 w-full sm:w-auto">
					{/* 公司代码信息区域 */}
					<div
						className={cn(
							"border rounded-md w-full sm:w-auto h-9 flex items-center", // 添加h-9高度和flex布局
							scale > 1.25 ? "px-2" : "px-3",
							styles.fontSize.content,
						)}
					>
						<div className="flex items-center">
							<span
								className={cn(
									"text-blue-600 mr-2 whitespace-nowrap",
									styles.fontSize.content,
								)}
							>
								代码:
							</span>
							<span
								className={cn(
									"text-blue-600 truncate",
									styles.fontSize.content,
								)}
							>
								{companyCode || "-"}
							</span>
						</div>
					</div>

					{/* 公司简称信息区域 */}
						<div
							className={cn(
								"border rounded-md w-full sm:w-auto h-9 flex items-center", // 添加h-9高度和flex布局
								scale > 1.25 ? "px-2" : "px-3",
								styles.fontSize.content,
							)}
						>
							<div className="flex items-center">
								<span
									className={cn(
										"text-blue-600 mr-2 whitespace-nowrap",
										styles.fontSize.content,
									)}
								>
									简称:
								</span>
								<span
									className={cn(
										"text-blue-600 truncate",
										styles.fontSize.content,
									)}
								>
									{companyName || "-"}
								</span>
							</div>
						</div>
				</div>

				{/* 按钮组 - 确保在窄屏幕下也能正常显示 */}
				<div className="flex items-center gap-2 self-end sm:self-auto mt-2 sm:mt-0">
					{/* 名册导入按钮 */}
					<Button
						variant="outline"
						size="icon"
						className={cn(
							"shrink-0",
							"h-9 w-9", // 确保高宽一致为9（36px）
							"rounded-md", // 与其他按钮保持一致的圆角
							"bg-background text-foreground border-border hover:bg-accent", // 使用默认白色背景
						)}
						onClick={onImport}
						aria-label="导入名册"
					>
						<UploadIcon className="size-4 m-0" />
					</Button>

					{/* 批量删除按钮 - 改为纯图标按钮 */}
					<Button
						variant="outline"
						size="icon"
						className={cn(
							"shrink-0",
							"h-9 w-9", // 确保高宽一致为9（36px）
							"rounded-md", // 与其他按钮保持一致的圆角
							batchDeleteConfirm
								? "bg-red-50 text-red-600 border-red-200 hover:bg-red-100"
								: "bg-background text-foreground border-border hover:bg-accent", // 使用默认白色背景
						)}
						onClick={handleBatchDeleteClick}
						title={batchDeleteConfirm ? "确认删除" : "批量删除"}
						disabled={isLoading || selectedCount === 0}
						aria-label={batchDeleteConfirm ? "确认删除" : "批量删除"}
					>
						{isLoading ? (
							<Loader2 className={cn(
								"animate-spin",
								scale > 1.25 ? "size-3" : "size-4"
							)} />
						) : batchDeleteConfirm ? (
							<Trash2 className={cn(
								scale > 1.25 ? "size-3" : "size-4"
							)} />
						) : (
							<X className={cn(
								scale > 1.25 ? "size-3" : "size-4"
							)} />
						)}
					</Button>
				</div>
			</div>
		</div>
	);
}
