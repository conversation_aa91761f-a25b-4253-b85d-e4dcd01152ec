// import { FaqSection } from "@marketing/home/<USER>/FaqSection";
// import { Features } from "@marketing/home/<USER>/Features";
// import { Hero } from "@marketing/home/<USER>/Hero";
// import { Newsletter } from "@marketing/home/<USER>/Newsletter";
// import { PricingSection } from "@marketing/home/<USER>/PricingSection";
import { setRequestLocale } from "next-intl/server";
import { HeroSection } from "@marketing/home/<USER>/HeroSection";
import { ClaudeModelsSection } from "@marketing/home/<USER>/ClaudeModelsSection";
import { WhyUsSection } from "@marketing/home/<USER>/WhyUsSection";
import { CustomerStoriesSection } from "@marketing/home/<USER>/CustomerStoriesSection";
import { OurPartners } from "@marketing/home/<USER>/OurPartners";
import { FAQSection } from "@marketing/home/<USER>/FaqSection";
import { CareersSection } from "@marketing/home/<USER>/CareersSection";
// import { Footer } from "@marketing/home/<USER>/Footer";

export default async function Home({
	params,
}: {
	params: Promise<{ locale: string }>;
}) {
	const { locale } = await params;
	setRequestLocale(locale);

	return (
		<>
			<HeroSection />
			<ClaudeModelsSection />
			<WhyUsSection />
			<CustomerStoriesSection />
			<OurPartners />
			<FAQSection />
			<CareersSection />
			{/* <PricingSection />
			<Newsletter /> */}
			{/* <Footer /> */}
		</>
	);
}
