import { getActiveOrganization } from "@saas/auth/lib/server";
import { notFound } from "next/navigation";
import { SettingsList } from "@saas/shared/components/SettingsList"; // 设置列表组件，用于布局
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@ui/components/card"; // UI卡片组件
import { BoxesIcon } from "lucide-react"; // 图标组件
/**
 * 生成页面元数据
 * @param params - 包含组织 slug 的参数对象
 * @returns 页面元数据对象
 */
export async function generateMetadata({
	params,
}: {
	params: Promise<{ organizationSlug: string }>;
}) {
	const { organizationSlug } = await params;

	const activeOrganization = await getActiveOrganization(
		organizationSlug as string,
	);

	return {
		title: `行业对标 - ${activeOrganization?.name}`,
	};
}

/**
 * 行业对标页面组件
 * 展示企业在行业中的排名、市值对比、估值对比和增长对比等信息
 * @param params - 包含组织 slug 的路由参数
 * @returns 渲染的页面组件
 */
export default async function IndustryIndustryPage({
	params,
}: { params: Promise<{ organizationSlug: string }> }) {
	// 获取路由参数中的组织 slug
	const { organizationSlug } = await params;

	// 获取当前激活的组织信息
	const activeOrganization = await getActiveOrganization(
		organizationSlug as string,
	);

	// 如果未找到组织则返回 404
	if (!activeOrganization) {
		return notFound();
	}

	return (
		<SettingsList>
			<Card>
				<CardHeader>
					{/* 卡片标题区域，包含图标和标题 */}
					<div className="flex items-center gap-2">
						<BoxesIcon className="size-5 text-primary" /> {/* 使用BoxesIcon作为功能图标 */}
						<CardTitle>行业对标</CardTitle> {/* 卡片主标题 */}
					</div>
					{/* 卡片描述，说明功能状态 */}
					<CardDescription>
						此功能模块正在开发中，敬请期待
					</CardDescription>
				</CardHeader>
				<CardContent>
					{/* 卡片内容区域，显示功能开发中的占位信息 */}
					<div className="flex h-32 items-center justify-center rounded-md border border-dashed border-muted-foreground/20 bg-muted/20">
						<p className="text-center text-sm text-muted-foreground">
							功能开发中，即将上线
							<br />
							<span className="mt-2 block text-xs">敬请期待更多精彩内容</span>
						</p>
					</div>
				</CardContent>
			</Card>
		</SettingsList>
	);
} 