import { z } from "zod";

/**
 * 上传股东名册的请求参数验证模式
 */
export const UploadRegistrySchema = z.object({
	organizationId: z.string().min(1, "组织ID不能为空"),
	fileName: z.string().min(1, "文件名不能为空"),
	recordCount: z.number().int().positive("记录数量必须为正整数"),
	registerDate: z
		.string()
		.regex(/^\d{4}-\d{2}-\d{2}$/, "报告日期格式必须为YYYY-MM-DD"),
	companyCode: z.string().min(1, "公司代码不能为空"),
	companyInfo: z.object({
		companyName: z.string().optional().default(""),
		totalShares: z.string().min(1, "总股数不能为空"),
		totalShareholders: z.number().int().nonnegative("总户数必须为非负整数"),
		totalInstitutions: z
			.number()
			.int()
			.nonnegative("机构总数必须为非负整数"),
		largeSharesCount: z.string().min(1, "持有万份以上总份数不能为空"),
		institutionShares: z.string().min(1, "总机构股数不能为空"),
		largeShareholdersCount: z
			.number()
			.int()
			.nonnegative("持有万份以上总户数必须为非负整数"),
	}),
	shareholders: z
		.array(
			z.object({
				shareholderId: z.string().min(1, "证件号码不能为空"),
				unifiedAccountNumber: z
					.string()
					.min(1, "一码通账户号码不能为空"),
				securitiesAccountName: z
					.string()
					.min(1, "证券账户名称不能为空"),
				shareholderCategory: z.string().min(1, "持有人类别不能为空"),
				numberOfShares: z.string().min(1, "持股数量不能为空"),
				lockedUpShares: z.string().min(1, "限售股数量不能为空"),
				shareholdingRatio: z.string().min(1, "持股比例不能为空"),
				frozenShares: z.string().min(1, "冻结股数不能为空"),
				// 以下字段为可选
				cashAccount: z.string().optional(),
				sharesInCashAccount: z.string().optional(),
				marginAccount: z.string().optional(),
				sharesInMarginAccount: z.string().optional(),
				contactAddress: z.string().optional(),
				contactNumber: z.string().optional(),
				zipCode: z.string().optional(),
				relatedPartyIndicator: z.string().optional(),
				clientCategory: z.string().optional(),
				remarks: z.string().optional(),
				// 新增05名册特有字段
				marginCollateralAccountNumber: z.string().optional(), // 汇总账户号码
				marginCollateralAccountName: z.string().optional(), // 汇总账户名称
				natureOfShares: z.string().optional(), // 股份性质
			})
		)
		.min(1, "股东列表不能为空"),
	// 新增名册类型字段，用于区分01和05名册
	registryType: z.enum(["01", "05"]).optional(), // 名册类型，可选，从文件名解析
});

/**
 * 获取股东名册列表的请求参数验证模式
 */
export const ListRegistrySchema = z.object({
  organizationId: z.string().min(1, "组织ID不能为空"),
  page: z.number().int().positive().optional().default(1),
  limit: z.number().int().positive().optional().default(10),
  companyCode: z.string().optional()
});

/**
 * 获取股东列表的请求参数验证模式
 */
export const ShareholdersSchema = z.object({
	registerDate: z
		.union([
			z
				.string()
				.regex(/^\d{4}-\d{2}-\d{2}$/, "报告日期格式必须为YYYY-MM-DD"),
			z.string().length(0), // 允许空字符串
			z.null(), // 允许null
		])
		.optional(), // 允许undefined
	organizationId: z.string().min(1, "组织ID不能为空"),
	page: z.number().int().positive().optional().default(1),
	limit: z.number().int().positive().optional().default(10),
	searchTerm: z.string().optional(),
	sortBy: z.string().optional().default("numberOfShares"),
	sortOrder: z.enum(["asc", "desc"]).optional().default("desc"),
});

/**
 * 期数日期查询请求模式
 */
export const RegisterDatesSchema = z.object({
  organizationId: z.string().min(1, "组织ID不能为空"),
  companyCode: z.string().optional()
});

/**
 * 删除股东名册的请求参数验证模式
 */
export const DeleteRegistrySchema = z.object({
  registryId: z.string().min(1, "名册ID不能为空")
});

/**
 * 批量删除股东名册的请求参数验证模式
 */
export const BatchDeleteRegistrySchema = z.object({
  registryIds: z.array(z.string().min(1, "名册ID不能为空")).min(1, "至少需要提供一个名册ID")
}); 