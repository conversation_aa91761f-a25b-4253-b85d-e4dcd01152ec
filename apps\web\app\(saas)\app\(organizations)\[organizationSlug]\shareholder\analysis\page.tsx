// 导入必要的依赖
import { getSession } from "@saas/auth/lib/server"; // 用于获取用户会话信息
import { SettingsList } from "@saas/shared/components/SettingsList"; // 设置列表组件，用于布局
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@ui/components/card"; // UI卡片组件
import { redirect } from "next/navigation"; // 页面重定向功能
import { BoxesIcon } from "lucide-react"; // 图标组件

/**
 * 股东名册分析页面组件
 * 这是一个服务器组件，用于显示股东名册分析功能的页面
 */
export default async function Feature1Page() {
	// 获取用户会话信息，用于验证用户是否已登录
	const session = await getSession();

	// 如果用户未登录，重定向到登录页面
	if (!session) {
		return redirect("/auth/login");
	}

	// 渲染页面内容
	return (
		<SettingsList>
			<Card>
				<CardHeader>
					{/* 卡片标题区域，包含图标和标题 */}
					<div className="flex items-center gap-2">
						<BoxesIcon className="size-5 text-primary" /> {/* 使用BoxesIcon作为功能图标 */}
						<CardTitle>股东名册分析</CardTitle> {/* 卡片主标题 */}
					</div>
					{/* 卡片描述，说明功能状态 */}
					<CardDescription>
						此功能模块正在开发中，敬请期待
					</CardDescription>
				</CardHeader>
				<CardContent>
					{/* 卡片内容区域，显示功能开发中的占位信息 */}
					<div className="flex h-32 items-center justify-center rounded-md border border-dashed border-muted-foreground/20 bg-muted/20">
						<p className="text-center text-sm text-muted-foreground">
							功能开发中，即将上线
							<br />
							<span className="mt-2 block text-xs">敬请期待更多精彩内容</span>
						</p>
					</div>
				</CardContent>
			</Card>
		</SettingsList>
	);
} 