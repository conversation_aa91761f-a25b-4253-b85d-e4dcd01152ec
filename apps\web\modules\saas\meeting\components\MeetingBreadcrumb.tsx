"use client";

import type { BreadcrumbItem } from "@saas/shared/components/SharedBreadcrumb";
import { SharedBreadcrumb } from "@saas/shared/components/SharedBreadcrumb";
import { usePathname, useParams } from "next/navigation";
import { useMemo } from "react";

/**
 * 会议模块的面包屑导航组件
 * 
 * @param organizationSlug - 组织的 slug
 * @param currentPage - 可选的当前页面名称，如果不提供则根据路径自动确定
 * @param showBackLink - 是否显示返回上一页链接
 * @returns 渲染的面包屑导航组件
 */
export function MeetingBreadcrumb({ 
  organizationSlug, 
  currentPage: providedCurrentPage,
}: { 
  organizationSlug: string; 
  currentPage?: string;
  showBackLink?:boolean;
}) {
  const pathname = usePathname(); // 获取当前路径
  const params = useParams(); // 获取路由参数
  
  // 解析路径，确定页面层次结构
  const pathSegments = useMemo(() => {
    // 从路径中提取会议模块之后的部分
    const meetingIndex = pathname.indexOf('/meeting');
    if (meetingIndex === -1) {
      return [];
    }
    
    const meetingPath = pathname.slice(meetingIndex + '/meeting'.length);
    // 分割路径部分，过滤掉空字符串
    return meetingPath.split('/').filter(Boolean);
  }, [pathname]);
  
  // 获取页面名称映射
  const pageNameMap: Record<string, string> = {
    'list': '会议列表',
    'personnel': '会议管理',
    'report': '会议报告',
    'schedule': '预定会议',
    'change_meeting': '修改会议'
  };
  
  // 根据路径确定当前页面名称
  const currentPage = useMemo(() => {
    if (providedCurrentPage) {
      return providedCurrentPage;
    }
    
    // 检查是否为修改会议页面路径: /meeting/list/change_meeting/{id}
    if (pathSegments.length >= 3 && 
        pathSegments[0] === 'list' && 
        pathSegments[1] === 'change_meeting') {
      return '修改会议';
    }
    
    // 如果有路径段，使用最后一个路径段确定当前页面
    if (pathSegments.length > 0) {
      const lastSegment = pathSegments[pathSegments.length - 1];
      return pageNameMap[lastSegment] || lastSegment;
    }
    
    return "会议";
  }, [pathSegments, providedCurrentPage, pageNameMap]);
  
  // 构建面包屑项目
  const breadcrumbItems: BreadcrumbItem[] = useMemo(() => {
    // 基础面包屑项目：应用 > 会议
    const items: BreadcrumbItem[] = [
      {
        label: "会议路演",
        href: `/app/${organizationSlug}/meeting/list`
      }
    ];
    
    // 检查是否为修改会议页面路径: /meeting/list/change_meeting/{id}
    const isChangeMeetingPage = pathSegments.length >= 3 && 
                                pathSegments[0] === 'list' && 
                                pathSegments[1] === 'change_meeting';
    
    if (isChangeMeetingPage) {
      // 对于修改会议页面，只添加会议列表中间层级，然后直接跳到修改会议
      items.push({
        label: "会议列表",
        href: `/app/${organizationSlug}/meeting/list`
      });
    } else {
      // 处理其他页面的子路径层级关系
      const basePath = `/app/${organizationSlug}/meeting`;
      let currentPath = basePath;
      
      // 如果存在多级路径，逐级添加中间层级
      if (pathSegments.length > 1) {
        // 遍历除了最后一个路径段外的所有路径段
        for (let i = 0; i < pathSegments.length - 1; i++) {
          const segment = pathSegments[i];
          currentPath += `/${segment}`;
          
          // 添加中间层级到面包屑
          const segmentName = pageNameMap[segment] || segment;
          items.push({
            label: segmentName,
            href: currentPath
          });
        }
      }
    }
    
    // 添加当前页面
    items.push({
      label: currentPage,
      isActive: true
    });
    
    return items;
  }, [organizationSlug, pathSegments, currentPage, pageNameMap]);
  
  return (
    <SharedBreadcrumb
      items={breadcrumbItems}
    />
  );
} 