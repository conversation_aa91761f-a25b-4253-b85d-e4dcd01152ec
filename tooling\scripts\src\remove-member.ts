import { db } from "@repo/database";
import { logger } from "@repo/logs";

async function main() {
	logger.info("🗑️  Remove Organization Member - 删除组织成员");
	logger.warn("⚠️  警告：此操作将从组织中移除指定成员，无法恢复！");

	// 获取用户标识 (支持邮箱或ID)
	const userIdentifier = await logger.prompt("请输入要移除的用户邮箱或ID:", {
		required: true,
		placeholder: "<EMAIL> 或 user_id_123",
		type: "text",
	});

	// 查找用户
	logger.info("🔍 正在查找用户...");
	
	const user = await db.user.findFirst({
		where: {
			OR: [
				{ email: userIdentifier },
				{ id: userIdentifier },
			],
		},
		include: {
			memberships: {
				include: {
					organization: {
						select: {
							id: true,
							name: true,
							slug: true,
							members: {
								select: {
									id: true,
									role: true,
									user: {
										select: {
											name: true,
											email: true,
										},
									},
								},
							},
						},
					},
				},
			},
		},
	});

	if (!user) {
		logger.error("❌ 未找到指定的用户！");
		return;
	}

	// 显示用户信息
	logger.info("📋 找到用户信息:");
	logger.info(`   ID: ${user.id}`);
	logger.info(`   姓名: ${user.name}`);
	logger.info(`   邮箱: ${user.email}`);

	// 检查用户的组织成员身份
	if (user.memberships.length === 0) {
		logger.warn("⚠️  该用户不是任何组织的成员！");
		return;
	}

	logger.info("\n🏢 用户的组织成员身份:");
	user.memberships.forEach((membership, index) => {
		const totalMembers = membership.organization.members.length;
		const ownerCount = membership.organization.members.filter(m => m.role === "owner").length;
		
		logger.info(`   ${index + 1}. ${membership.organization.name} (${membership.organization.slug || membership.organization.id})`);
		logger.info(`      当前角色: ${membership.role}`);
		logger.info(`      组织成员总数: ${totalMembers} 人`);
		logger.info(`      组织所有者数量: ${ownerCount} 人`);
		logger.info(`      加入时间: ${membership.createdAt.toLocaleString()}`);
	});

	// 选择要移除的组织成员身份
	let selectedMembership: any;
	if (user.memberships.length === 1) {
		selectedMembership = user.memberships[0];
		logger.info(`\n✅ 自动选择唯一的组织: ${selectedMembership.organization.name}`);
	} else {
		const organizationChoice = await logger.prompt(
			"请选择要移除的组织成员身份 (输入序号):",
			{
				required: true,
				type: "text",
				placeholder: "1",
			}
		);

		const choiceIndex = Number.parseInt(organizationChoice) - 1;
		if (Number.isNaN(choiceIndex) || choiceIndex < 0 || choiceIndex >= user.memberships.length) {
			logger.error("❌ 无效的选择！");
			return;
		}

		selectedMembership = user.memberships[choiceIndex];
	}

	// 安全检查：如果是组织的唯一所有者，需要特别警告
	const organization = selectedMembership.organization;
	const ownerMembers = organization.members.filter((m: any) => m.role === "owner");
	const isOnlyOwner = selectedMembership.role === "owner" && ownerMembers.length === 1;

	if (isOnlyOwner) {
		logger.error("🚨 严重警告：该用户是组织的唯一所有者！");
		logger.error("   移除此用户将导致组织失去所有者，可能影响组织正常运行！");
		logger.warn("   建议先将所有者权限转移给其他成员，再执行移除操作。");
		
		const confirmRemoveOnlyOwner = await logger.prompt(
			"⚠️  确认要移除组织的唯一所有者吗？这是一个高风险操作！",
			{
				required: true,
				type: "confirm",
				default: false,
			}
		);

		if (!confirmRemoveOnlyOwner) {
			logger.info("✅ 操作已取消，建议先转移所有者权限。");
			return;
		}
	}

	// 显示即将移除的成员信息
	logger.info("\n📊 即将移除的成员信息:");
	logger.info(`   组织: ${organization.name}`);
	logger.info(`   用户: ${user.name} (${user.email})`);
	logger.info(`   角色: ${selectedMembership.role}`);
	logger.info(`   加入时间: ${selectedMembership.createdAt.toLocaleString()}`);
	logger.info(`   成员ID: ${selectedMembership.id}`);

	// 最终确认
	const confirmRemove = await logger.prompt(
		`❓ 确认从 ${organization.name} 中移除用户 ${user.name} 吗？此操作不可逆！`,
		{
			required: true,
			type: "confirm",
			default: false,
		}
	);

	if (!confirmRemove) {
		logger.info("✅ 操作已取消，成员未被移除。");
		return;
	}

	// 二次确认 (对于所有者角色)
	if (selectedMembership.role === "owner") {
		const doubleConfirm = await logger.prompt(
			"🚨 这是一个高风险操作！确认移除组织所有者？",
			{
				required: true,
				type: "confirm",
				default: false,
			}
		);

		if (!doubleConfirm) {
			logger.info("✅ 操作已取消，成员未被移除。");
			return;
		}
	}

	// 执行移除操作
	logger.info("🔄 正在移除组织成员...");

	try {
		const removedMember = await db.member.delete({
			where: {
				id: selectedMembership.id,
			},
			include: {
				user: {
					select: {
						name: true,
						email: true,
					},
				},
				organization: {
					select: {
						name: true,
					},
				},
			},
		});

		logger.success("✅ 组织成员移除成功！");
		logger.info("📋 移除操作摘要:");
		logger.info(`   - 组织: ${removedMember.organization.name}`);
		logger.info(`   - 用户: ${removedMember.user.name} (${removedMember.user.email})`);
		logger.info(`   - 移除的角色: ${selectedMembership.role}`);
		logger.info(`   - 移除时间: ${new Date().toLocaleString()}`);
		
		if (isOnlyOwner) {
			logger.warn("⚠️  注意：组织现在没有所有者，请尽快指定新的所有者！");
		}

	} catch (error) {
		logger.error("❌ 移除组织成员时发生错误:");
		logger.error(error);
		logger.error("成员未被移除，请检查错误信息后重试。");
	}
}

main().catch((error) => {
	logger.error("脚本执行失败:");
	logger.error(error);
	process.exit(1);
});
