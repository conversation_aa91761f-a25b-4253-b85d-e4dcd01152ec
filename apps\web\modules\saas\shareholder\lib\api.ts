import { apiClient } from "@shared/lib/api-client";
import { decryptData, createEncryptedRequestAsync } from "@repo/utils";

// 定义API响应类型
interface ApiResponse {
	code: number;
	message: string;
	data: string | any;
}

// 定义股东数据结构
interface ShareholderData {
	shareholders: Array<Record<string, any>>;
	total?: number;
	page?: number;
	limit?: number;
	[key: string]: any;
}

// 导出股东API客户端
export const shareholderApi = {
	/**
	 * 获取股东列表
	 * @param organizationId 组织ID
	 * @param params 查询参数
	 * @returns 股东列表
	 */
	getShareholderList: async (
		organizationId: string, 
		params?: {
			registerDate?: string;
			page?: number;
			limit?: number;
			searchTerm?: string;
			sortBy?: string;
			sortOrder?: 'asc' | 'desc';
		}
	): Promise<ShareholderData> => {
		try {
			// 创建加密且带签名的请求参数
			const requestData = await createEncryptedRequestAsync({ 
				organizationId,
				...params
			});

			// 发送请求到股东查询接口 (使用股东名册的股东查询接口)
			const response = await apiClient["shareholder-registry"].shareholders.$post({
				json: requestData
			});

			// 处理响应
			const responseData = await response.json() as ApiResponse;
			
			let resultData: ShareholderData = { shareholders: [] };
			
			// 如果data是字符串，直接解密
			if (typeof responseData.data === 'string') {
				const decryptedData = decryptData(responseData.data);
				resultData = (typeof decryptedData === 'object' ? decryptedData : {}) as ShareholderData;
				
				// 如果返回的是字符串，尝试解析为JSON对象
				if (typeof decryptedData === 'string') {
					try {
						resultData = JSON.parse(decryptedData) as ShareholderData;
					} catch (e) {
						// 解析失败，保持原样
						resultData = { shareholders: [] };
					}
				}
			} 
			// 否则返回原始数据
			else {
				resultData = responseData.data as ShareholderData;
			}
			
			// 验证数据结构
			if (resultData && typeof resultData === 'object') {
				if (!resultData.shareholders) {
					// 股东字段缺失
					resultData.shareholders = [];
				} else if (!Array.isArray(resultData.shareholders)) {
					// 尝试将shareholders转换为数组形式
					if (resultData.shareholders && typeof resultData.shareholders === 'object') {
						resultData.shareholders = [resultData.shareholders];
					}
				}
			}
			
			return resultData;
		} catch (error) {
			// 返回错误对象，不打印错误到控制台
			return { shareholders: [] };
		}
	},
};
