"use client";

import { Trash2Icon, FileTextIcon, XIcon } from "lucide-react";
import { 
  Button 
} from "@ui/components/button";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
  TooltipPortal,
} from "@ui/components/tooltip";
import { TablePagination } from "@saas/shareholder/components/TablePagination";
import type { ShareholderRegistryItem, PaginationInfo } from "@saas/shareholder/lib/types";
import { formatDate, formatNumber } from "@saas/shareholder/lib/utils";
import { cn } from "@ui/lib";
import React, { useState, useCallback, useEffect, useMemo } from "react";
import { useSystemScale } from "@saas/shareholder/hooks/useSystemScale";
import { AntShareholderTable } from "@saas/shareholder/components/ant-shareholder-table";
import type { ShareholderColumn, ShareholderRowSelection } from "@saas/shareholder/components/ant-shareholder-table";

// 删除状态枚举
type DeleteState = "normal" | "confirm";

// 为表格数据定义一个自定义类型，继承自组件所需的类型
interface RegistryTableItem {
  id: string;
  period: string;
  totalShares: string;
  accountCount: string;
  trustAccount: string;
  control: React.ReactNode;
  topTen: React.ReactNode;
  trustShares: string;
  registry: React.ReactNode;
  importDate: string;
  user: string;
  // selected属性不再需要，将由AntShareholderTable内部管理
  originalData: ShareholderRegistryItem;
  // 添加ShareholderItem所需的必要属性
  name: string;
  shares: number | string;
}

interface ShareholderRegistryTableProps {
  registries: ShareholderRegistryItem[];
  pagination?: PaginationInfo;
  isLoading?: boolean;
  page: number;
  limit: number;
  onPageChange: (page: number) => void;
  onLimitChange?: (limit: number) => void;
  onDeleteRegistry: (registry: ShareholderRegistryItem) => void;
  selectedItems: string[];
  onSelectItem: (id: string, selected: boolean) => void;
  onSelectAll: (selected: boolean) => void;
  disableSelection?: boolean;
}

/**
 * 空数据状态组件 - 提取为单独组件避免重复
 */
function EmptyState(text: string) {
  // 使用系统缩放hook进行样式适配
  const { scale } = useSystemScale();
  
  return (
			<div
				className="flex flex-col items-center justify-center py-12"
				style={{ height: "300px" }}
			>
				<p
					className={cn(
						"font-medium",
						scale > 1.25 ? "text-sm" : "text-base"
					)}
          
				>
					{text}
				</p>
			</div>
		);
}

/**
 * 直接显示文件名组件，不使用Tooltip
 */
function RegistryFileDisplay({ 
  fileName
}: { 
  fileName: string; 
}) {
  // 使用系统缩放hook进行样式适配
  const { scale, styles } = useSystemScale();
  
  // 检查fileName是否包含多个文件（通过换行符分隔）
  const fileNames = fileName.split('\n').filter(Boolean);

  return (
			<div className="flex flex-col items-center cursor-default w-full">
				{fileNames.map((name, index) => (
					<div
						key={index}
						className={cn(
							"flex items-center justify-center text-slate-700",
							styles.fontSize.content,
							index > 0 && "mt-1",
							"w-full"
						)}
					>
						<FileTextIcon
							className={cn(
								"flex-shrink-0 mr-1.5 text-blue-500",
								scale > 1.25 ? "h-2.5 w-2.5" : "h-3 w-3",
							)}
						/>
						<span className="text-blue-500 text-center break-all">
							{name}
						</span>
					</div>
				))}
			</div>
		);
}

/**
 * 计算控股比例（大股东的持股比例）
 */
function calculateControllingRatio(registry: ShareholderRegistryItem): string {
  // 获取控股股东信息
  const controllingInfo = registry.companyDetail?.controllingShareholderInfo;
  if (controllingInfo?.shareholdingRatio) {
    // 如果存在持股比例，返回格式化后的比例
    return `${controllingInfo.shareholdingRatio}%`;
  }
  return "-";
}

/**
 * 计算前十大股东持股比例总和
 */
function calculateTopTenRatio(registry: ShareholderRegistryItem): string {
  // 获取前十大股东信息
  const topTenInfo = registry.companyDetail?.topTenShareholdersInfo;
  if (topTenInfo?.totalRatio) {
    // 如果存在总比例，返回格式化后的比例
    return `${topTenInfo.totalRatio}%`;
  }
  return "-";
}

/**
 * 控股股东信息Tooltip组件
 */
function ControllingShareholderTooltip({
  registry
}: {
  registry: ShareholderRegistryItem;
}) {
  // 使用系统缩放hook进行样式适配
  const { scale, styles } = useSystemScale();
  
  // 获取控股股东信息
  const controllingInfo = registry.companyDetail?.controllingShareholderInfo;
  
  // 如果没有控股股东信息，直接返回比例值
  if (!controllingInfo) {
    return <span>{calculateControllingRatio(registry)}</span>;
  }
  
  return (
    <TooltipProvider>
      <Tooltip delayDuration={300}>
        <TooltipTrigger asChild>
          <span className={cn(
            "inline-flex items-center justify-center text-blue-600 hover:text-blue-700 font-normal rounded-md transition-colors cursor-default",
            styles.fontSize.content
          )}>
            {controllingInfo.shareholdingRatio}%
          </span>
        </TooltipTrigger>
        <TooltipPortal>
          <TooltipContent side="top" align="start" className="max-w-[300px]">
            <div className={cn(
              styles.fontSize.content
            )}>
              <p className="font-medium mb-1">控股股东信息</p>
              <p>股东名称: {controllingInfo.securitiesAccountName}</p>
              <p>持股比例: {controllingInfo.shareholdingRatio}%</p>
              <p>持股数量: {formatNumber(Number(controllingInfo.numberOfShares), { decimals: 0 })}</p>
            </div>
          </TooltipContent>
        </TooltipPortal>
      </Tooltip>
    </TooltipProvider>
  );
}

/**
 * 前十大股东信息Tooltip组件
 */
function TopTenShareholdersTooltip({
  registry
}: {
  registry: ShareholderRegistryItem;
}) {
  // 使用系统缩放hook进行样式适配
  const { scale, styles } = useSystemScale();
  
  // 获取前十大股东信息
  const topTenInfo = registry.companyDetail?.topTenShareholdersInfo;
  
  // 如果没有前十大股东信息，直接返回比例值
  if (!topTenInfo) {
    return <span>{calculateTopTenRatio(registry)}</span>;
  }
  
  return (
    <TooltipProvider>
      <Tooltip delayDuration={300}>
        <TooltipTrigger asChild>
          <span className={cn(
            "inline-flex items-center justify-center text-blue-600 hover:text-blue-700 font-normal rounded-md transition-colors cursor-default",
            styles.fontSize.content
          )}>
            {topTenInfo.totalRatio}%
          </span>
        </TooltipTrigger>
        <TooltipPortal>
          <TooltipContent side="top" align="start" className="max-w-[300px]">
            <div className={cn(
              styles.fontSize.content
            )}>
              <p className="font-medium mb-1">前十大股东持股汇总</p>
              <p>持股比例总和: {topTenInfo.totalRatio}%</p>
              <p>持股数量总和: {formatNumber(Number(topTenInfo.totalShares), { decimals: 0 })}</p>
              <p className="text-xs text-slate-500 mt-1">前十大股东合计占总股本的比例</p>
            </div>
          </TooltipContent>
        </TooltipPortal>
      </Tooltip>
    </TooltipProvider>
  );
}

/**
 * 股东名册表格组件
 * 显示已上传的股东名册列表，支持选择、分页、详情查看和删除操作
 * 
 * @version 4.0.2 (2025-05-22) - 优化空数据展示逻辑，移除冗余的条件渲染
 * @version 4.0.1 (2025-05-22) - 优化加载状态显示，确保表格尺寸一致性
 * @version 4.0.0 (2025-05-22) - 重构为使用通用的AntShareholderTable组件，保持功能不变
 * @version 3.3.0 (2025-05-22) - 添加控股股东和前十大股东数据展示，添加Tooltip交互
 * @version 3.2.0 (2025-05-19) - 添加系统缩放适配，优化在高DPI显示器上的视觉体验
 * @version 3.1.0 (2025-05-19) - 添加公司详细信息字段展示，包括总股本、总户数等
 * @version 3.0.0 (2025-05-19) - 改进跨分辨率显示支持，优化滚动体验，增加期数列宽度
 * @version 2.0.0 (2025-05-19) - 移除移动端适配逻辑，只保留桌面端布局
 */
export function ShareholderRegistryTable({
  registries,
  pagination,
  page,
  onPageChange,
  onLimitChange,
  onDeleteRegistry,
  selectedItems,
  onSelectItem,
  onSelectAll,
  disableSelection = false,
  isLoading = false
}: ShareholderRegistryTableProps) {
  // 使用系统缩放hook获取缩放比例和样式配置
  const { scale, styles, tableStyles } = useSystemScale();
  
  // 验证名册数据
  const validRegistries = Array.isArray(registries) ? registries : [];
  
  // 全选状态计算
  const allSelected = validRegistries.length > 0 && validRegistries.every(reg => selectedItems.includes(reg.id));
  const someSelected = validRegistries.length > 0 && validRegistries.some(reg => selectedItems.includes(reg.id)) && !allSelected;
  
  // 添加删除确认状态 - 保存处于"删除确认"状态的记录ID和阶段
  const [deletingStatus, setDeletingStatus] = useState<{ id: string | null; state: DeleteState }>({
    id: null,
    state: "normal"
  });
  
  // 清除删除确认状态的定时器ID
  const [deleteConfirmTimer, setDeleteConfirmTimer] = useState<NodeJS.Timeout | null>(null);
  
  // 处理删除按钮点击
  const handleDeleteClick = useCallback((registry: ShareholderRegistryItem) => {
    // 如果当前点击的是已经处在删除流程中的记录
    if (deletingStatus.id === registry.id) {
      // 如果当前是确认阶段，执行删除操作
      onDeleteRegistry(registry);
      
      // 清除定时器
      if (deleteConfirmTimer) {
        clearTimeout(deleteConfirmTimer);
        setDeleteConfirmTimer(null);
      }
      
      // 重置删除状态
      setDeletingStatus({
        id: null,
        state: "normal"
      });
    } else {
      // 如果是新的记录，进入确认阶段
      // 先清除之前的定时器
      if (deleteConfirmTimer) {
        clearTimeout(deleteConfirmTimer);
      }
      
      // 设置为确认阶段
      setDeletingStatus({
        id: registry.id,
        state: "confirm"
      });
      
      // 设置3秒后自动取消确认状态的定时器
      const timerId = setTimeout(() => {
        setDeletingStatus({
          id: null,
          state: "normal"
        });
      }, 3000);
      
      setDeleteConfirmTimer(timerId);
    }
  }, [deletingStatus, deleteConfirmTimer, onDeleteRegistry]);
  
  // 组件卸载时清除定时器
  useEffect(() => {
    return () => {
      if (deleteConfirmTimer) {
        clearTimeout(deleteConfirmTimer);
      }
    };
  }, [deleteConfirmTimer]);
  
  // 格式化时间
  const formatDateTime = (dateString: string) => {
    try {
      // 使用formatDate函数，设置为full格式，保证显示为"2025-05-20 13:55"的格式
      return formatDate(dateString, { 
        format: 'full',
        locale: 'zh-CN'
      }).replace(/(\d{4})\/(\d{2})\/(\d{2})/, '$1-$2-$3').replace(/:\d{2}$/, ''); // 替换格式并移除秒数
    } catch (e) {
      return dateString;
    }
  };

  // 格式化金额数字（例如：10,000,000）
  const formatShareAmount = (amount: string | undefined) => {
    if (!amount) {
      return "-";
    }
    
    try {
      // 处理科学计数法表示的数字
      const num = Number.parseFloat(amount);
      // 修改：使用formatNumber函数时设置decimals为0，不显示小数点
      return formatNumber(num, { decimals: 0 });
    } catch (e) {
      return amount;
    }
  };
  
  // 将注册表数据转换为AntShareholderTable需要的格式
  const tableData = useMemo(() => {
    return validRegistries.map(registry => ({
      id: registry.id,
      // ShareholderItem所需的必要属性
      name: registry.companyDetail?.companyName || registry.fileName || "名册",
      shares: registry.companyDetail?.totalShares || "0",
      // 自定义属性
      period: registry.registerDate,
      totalShares: registry.companyDetail ? formatShareAmount(registry.companyDetail.totalShares) : "-",
      accountCount: registry.companyDetail ? registry.companyDetail.totalShareholders.toLocaleString() : "-",
      trustAccount: registry.companyDetail ? registry.companyDetail.totalInstitutions.toLocaleString() : "-",
      control: <ControllingShareholderTooltip registry={registry} />,
      topTen: <TopTenShareholdersTooltip registry={registry} />,
      trustShares: registry.companyDetail ? formatShareAmount(registry.companyDetail.institutionShares) : "-",
      registry: <RegistryFileDisplay fileName={registry.fileName} />,
      importDate: formatDateTime(registry.uploadedAt),
      user: registry.userName || "未知用户",
      originalData: registry,
    })) as RegistryTableItem[];
  }, [validRegistries, formatDateTime]);
  
  // 处理表格行选择变化
  const handleSelectionChange = useCallback((selectedRowIds: string[]) => {
    // 如果是全选操作
    if (selectedRowIds.length === validRegistries.length && validRegistries.length > 0) {
      onSelectAll(true);
      return;
    }
    
    // 如果是全不选操作
    if (selectedRowIds.length === 0 && selectedItems.length > 0) {
      onSelectAll(false);
      return;
    }
    
    // 对于新选中的项目，调用onSelectItem
    selectedRowIds.forEach(id => {
      if (!selectedItems.includes(id)) {
        onSelectItem(id, true);
      }
    });
    
    // 对于取消选中的项目，调用onSelectItem
    selectedItems.forEach(id => {
      if (!selectedRowIds.includes(id)) {
        onSelectItem(id, false);
      }
    });
  }, [selectedItems, onSelectItem, onSelectAll, validRegistries.length]);
  
  // 创建行选择配置
  const rowSelection: ShareholderRowSelection = useMemo(() => {
    return {
      selectedRowKeys: selectedItems,
      onChange: handleSelectionChange,
      getCheckboxProps: () => ({
        disabled: disableSelection
      })
    };
  }, [selectedItems, handleSelectionChange, disableSelection]);
  
  // 定义列配置
  const columns: ShareholderColumn[] = useMemo(() => {
    // 创建一个通用的单元格类名 - 移除硬编码的文本颜色
    const cellClass = "text-center";
    
    return [
					{
						key: "period",
						title: "期数",
						width: 160,
						className: "text-center",
						render: (value) => (
							<span className={cellClass}>{value}</span>
						),
					},
					{
						key: "totalShares",
						title: "总股本",
						width: 80,
						className: "text-center",
						render: (value) => (
							<span className={cellClass}>{value}</span>
						),
					},
					{
						key: "accountCount",
						title: "总户数",
						width: 80,
						className: "text-center",
						render: (value) => (
							<span className={cellClass}>{value}</span>
						),
					},
					{
						key: "trustAccount",
						title: "信用户数",
						width: 80,
						className: "text-center",
						render: (value) => (
							<span className={cellClass}>{value}</span>
						),
					},
					{
						key: "control",
						title: "控股股东",
						width: 100,
						className: "text-center",
					},
					{
						key: "topTen",
						title: "前十股东",
						width: 100,
						className: "text-center",
					},
					{
						key: "trustShares",
						title: "信用股东",
						width: 100,
						className: "text-center",
						render: (value) => (
							<span className={cellClass}>{value}</span>
						),
					},
					{
						key: "registry",
						title: "名册",
						width: 300,
						className: "text-center",
						render: (value) => value,
					},
					{
						key: "importDate",
						title: "导入日期",
						width: 170,
						className: "text-center",
						render: (value) => (
							<span className={cellClass}>{value}</span>
						),
					},
					{
						key: "user",
						title: "上传用户",
						width: 120,
						className: "text-center",
						render: (value) => (
							<span className={cellClass}>{value}</span>
						),
					},
					{
						key: "action",
						title: "操作",
						width: 60,
						className: "text-center",
						render: (_, record) => (
							<div className="flex justify-center items-center">
								<Button
									variant="ghost"
									size="icon"
									className={cn(
										"transition-colors rounded-md",
										deletingStatus.id === record.id
											? "text-red-600 bg-red-50/80"
											: "text-slate-500 hover:text-red-600 hover:bg-red-50/80",
										scale > 1.25 ? "h-5 w-5" : "h-6 w-6",
									)}
									onClick={() =>
										handleDeleteClick(record.originalData)
									}
								>
									{deletingStatus.id === record.id ? (
										<Trash2Icon
											className={cn(
												"fill-red-100",
												scale > 1.25
													? "h-3 w-3"
													: "h-3.5 w-3.5",
											)}
										/>
									) : (
										<XIcon
											className={cn(
												scale > 1.25
													? "h-3 w-3"
													: "h-3.5 w-3.5",
											)}
										/>
									)}
								</Button>
							</div>
						),
					},
				];
  }, [scale, deletingStatus, handleDeleteClick]);
  
  // 定义加载状态的配置
  const loadingConfig = useMemo(() => {
    return {
      spinning: isLoading,
      size: "large" as const,
    };
  }, [isLoading]);
  
  // 移除冗余的条件渲染，直接使用 AntShareholderTable 进行渲染
		// AntShareholderTable 组件会根据数据是否为空自动显示 emptyContent
		return (
			<>
				<div
					className={cn(
						"rounded-md border border-slate-200 overflow-hidden",
						scale > 1.25 ? "shadow-xs" : "shadow-sm",
					)}
				>
					<AntShareholderTable
						data={tableData}
						columns={columns}
						loading={loadingConfig}
						className="w-full"
						headerClassName="sticky top-0 z-10 font-medium"
						rowClassName="transition-colors border-b border-slate-200/70 last:border-0"
						cellClassName={styles.fontSize.content}
						emptyContent={EmptyState(`${loadingConfig.spinning ? "" : "暂无股东名册数据"}`)}
						rowSelection={rowSelection}
					/>
				</div>

				{/* 使用分页组件 */}
				<div className={cn(scale > 1.25 ? "mt-4" : "mt-5")}>
					<TablePagination
						pagination={
							pagination || {
								total: 0,
								page: 1,
								limit: 10,
								totalPages: 1,
							}
						}
						page={page}
						onPageChange={onPageChange}
						onLimitChange={onLimitChange}
					/>
				</div>
			</>
		);
} 