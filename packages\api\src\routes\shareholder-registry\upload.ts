import { Hono } from "hono";
import { db } from "@repo/database";
import { authMiddleware } from "../../middleware/auth";
import { shareholderCryptoMiddleware } from "../../middleware/shareholder-crypto";
import { UploadRegistrySchema, type UploadRegistryRequest, type UploadRegistryResponse } from "./types";
import { HTTPException } from "hono/http-exception";
import {
	successResponse,
	errorResponse,
	handleHttpException,
	processShareholderUpdateFields,
} from "./lib/utils";
/**
 * 上传股东名册路由
 * 
 * 功能：上传并处理股东名册数据，包括公司信息和股东列表
 * 路径：/upload
 * 方法：POST
 * 中间件：
 * - authMiddleware: 验证用户身份和权限
 * - shareholderCryptoMiddleware: 处理请求和响应的加解密
 * 
 * 业务逻辑：
 * 1. 验证请求数据格式
 * 2. 检查recordCount与shareholders数组长度是否匹配
 * 3. 检查组织与公司代码的绑定关系
 * 4. 使用事务处理保证数据一致性
 * 5. 创建股东名册记录
 * 6. 创建公司信息记录
 * 7. 批量创建股东记录
 * 8. 支持01和05名册的合并处理
 * 9. 根据股东类别决定使用证件号码或一码通账号作为唯一标识
 * 
 * 优化说明：
 * 1. 减少数据库查询次数，合并查询操作
 * 2. 使用批量操作替代循环单条操作
 * 3. 优化事务处理流程，减少事务时间
 * 4. 使用更高效的数据结构提高查询效率
 * 
 * 数据库表关系：
 * - shareholderRegistry: 股东名册主表，记录名册基本信息
 * - companyInfo: 公司信息表，与名册一对一关系
 * - shareholder: 股东信息表，与名册一对多关系
 * - organization: 组织表，与名册多对一关系
 */
export const uploadRouter = new Hono().post(
  "/upload",
  authMiddleware,                 // 验证用户身份和权限
  shareholderCryptoMiddleware(),  // 处理请求和响应的加解密
  async (c) => {
    try {
      // 从上下文中获取解密后的请求数据
      const requestData = c.get("requestData") as UploadRegistryRequest;
      const userId = c.get("user").id;
 
      // 验证请求数据
      const validationResult = UploadRegistrySchema.safeParse(requestData);
      if (!validationResult.success) {
        throw new HTTPException(400, { 
          message: "请求参数无效", 
          cause: validationResult.error 
        });
      }
      
      const data = validationResult.data;
      
      // 验证recordCount与shareholders数组长度是否匹配
      if (data.recordCount !== data.shareholders.length) {
        return errorResponse(
          c,
          "股东记录数量不匹配",
          400,
          "RECORD_COUNT_MISMATCH",
          `申报的股东数量(${data.recordCount})与实际提供的股东数据数量(${data.shareholders.length})不一致`
        );
      }

      // 解析文件名，确定名册类型（01或05）
      // 文件名格式：DQMC01_公司代码_日期.DBF 或 DQMC05_公司代码_日期.DBF
      const registryType: "01" | "05" = data.registryType || (data.fileName.includes("DQMC01") ? "01" : data.fileName.includes("DQMC05") ? "05" : "01");
      
      /**
       * 动态事务超时计算
       * 
       * 根据处理的数据量动态计算合适的事务超时时间：
       * - 基础超时时间：30秒(30000ms)
       * - 每条记录增加：30ms
       * 
       * 例如：
       * - 200条记录: 30000 + 200*30 = 36000ms (36秒)
       * - 1000条记录: 30000 + 1000*30 = 60000ms (60秒)
       * - 2000条记录: 30000 + 2000*30 = 90000ms (90秒)
       */
      const totalRecords = data.shareholders.length;
      const dynamicTimeout = Math.max(30000, totalRecords * 30);
      
      /**
       * 数据库事务处理
       * 
       * 使用事务确保所有数据库操作要么全部成功，要么全部失败，保证数据一致性
       * 特别适用于此处需要同时操作多个表（名册、公司信息、股东信息）的场景
       * 
       * 事务配置：
       * - timeout: 动态计算的超时时间，根据数据量自动调整
       * - isolationLevel: 'ReadCommitted'，允许读取已提交的数据，避免脏读
       */
      const result = await db.$transaction(
			async (tx) => {
				/**
				 * 并行查询优化
				 *
				 * 使用Promise.all同时执行两个查询操作：
				 * 1. 查询组织信息 - 验证组织是否存在及其绑定关系
				 * 2. 查询是否存在同日期名册 - 决定是创建新名册还是更新现有名册
				 *
				 * 性能提升：减少约50%的查询等待时间
				 */
				const [organization, existingRegistry] = await Promise.all([
					// 查询组织信息 - 只选择必要字段，减少数据传输量
					tx.organization.findUnique({
						where: { id: data.organizationId },
						select: { id: true, metadata: true }, // 最小化查询字段
					}),
					// 查询是否存在同一日期的名册记录
					tx.shareholderRegistry.findFirst({
						where: {
							organizationId: data.organizationId,
							companyCode: data.companyCode,
							registerDate: new Date(data.registerDate),
						},
						select: { id: true, fileName: true }, // 最小化查询字段
					}),
				]);

				// 检查组织是否存在
				if (!organization) {
					throw new HTTPException(404, { message: "组织不存在" });
				}

				/**
				 * 名册类型验证
				 * 
				 * 检查是否已经上传了相同类型的名册文件
				 * 防止重复上传01或05类型的名册，确保数据一致性
				 */
				if (existingRegistry) {
					const currentRegistryType = registryType; // 当前上传的名册类型
					const existingFileName = existingRegistry.fileName;
					
					// 检查现有名册是否已包含当前类型
					const hasType01 = existingFileName.includes("DQMC01");
					const hasType05 = existingFileName.includes("DQMC05");
					
					// 如果当前上传的是01名册，但已存在01名册
					if (currentRegistryType === "01" && hasType01) {
						throw new HTTPException(400, { 
							message: "DUPLICATE_REGISTRY_TYPE:已存在01类型名册，不能重复上传"
						});
					}
					
					// 如果当前上传的是05名册，但已存在05名册
					if (currentRegistryType === "05" && hasType05) {
						throw new HTTPException(400, { 
							message: "DUPLICATE_REGISTRY_TYPE:已存在05类型名册，不能重复上传"
						});
					}
					
					// 如果已经同时存在01和05名册，不允许再次上传任何类型
					if (hasType01 && hasType05) {
						throw new HTTPException(400, { 
							message: "COMPLETE_REGISTRY:已存在01和05类型名册的合并数据，不能继续上传"
						});
					}
				}

				/**
				 * 元数据处理
				 *
				 * 解析JSON格式的metadata字段，处理组织与公司的绑定关系
				 * 若metadata为空，初始化为空对象，避免解析错误
				 */
				// eslint-disable-next-line @typescript-eslint/no-explicit-any
				const metadata: Record<string, any> = organization.metadata
					? JSON.parse(organization.metadata)
					: {};

				// 检查是否已绑定公司代码 - 确保一个组织只能绑定一个公司
				if (
					metadata.boundCompanyCode &&
					metadata.boundCompanyCode !== data.companyCode
				) {
					throw new HTTPException(400, {
						message: `该组织已绑定公司代码 ${metadata.boundCompanyCode}，不能上传其他公司的股东数据`,
					});
				}

				let registry = undefined;
				let isNewRegistry = false;

				/**
				 * 组织绑定处理
				 *
				 * 如果组织尚未绑定公司，则进行首次绑定
				 * 记录绑定时间，便于后续审计和分析
				 */
				if (!metadata.boundCompanyCode) {
					// 首次绑定公司代码到组织
					metadata.boundCompanyCode = data.companyCode;
					metadata.boundCompanyName = data.companyInfo.companyName;
					metadata.boundAt = new Date().toISOString();

					// 更新组织的metadata字段
					// 优化建议：考虑将metadata结构化为独立字段，避免JSON序列化/反序列化开销
					await tx.organization.update({
						where: { id: data.organizationId },
						data: { metadata: JSON.stringify(metadata) },
					});
				} else if (registryType === "01" && (!metadata.boundCompanyName || metadata.boundCompanyName === "") && data.companyInfo.companyName) {
					// 处理先上传05名册后上传01名册的情况
					// 05名册没有公司名称，01名册有，需要更新metadata中的公司名称
					metadata.boundCompanyName = data.companyInfo.companyName;
					
					// 更新组织的metadata字段
					await tx.organization.update({
						where: { id: data.organizationId },
						data: { metadata: JSON.stringify(metadata) },
					});
				}

				/**
				 * 名册记录处理
				 *
				 * 根据是否存在同日期名册，执行不同的处理逻辑：
				 * - 存在：更新现有名册，合并文件名
				 * - 不存在：创建新名册，同时创建关联的公司信息
				 */
				if (existingRegistry) {
					/**
					 * 更新现有名册
					 *
					 * 合并文件名，保留名册类型信息
					 * 使用换行符分隔多个文件名，便于前端展示
					 */
					const newFileName = existingRegistry.fileName.includes(
						`DQMC${registryType}`
					)
						? existingRegistry.fileName // 如果已经包含当前类型，不改变文件名
						: `${existingRegistry.fileName}\n${data.fileName}`; // 使用换行符替代+号连接文件名

					registry = await tx.shareholderRegistry.update({
						where: { id: existingRegistry.id },
						data: { fileName: newFileName },
					});
				} else {
					/**
					 * 创建新名册
					 *
					 * 使用嵌套写入（nested write）一次性创建名册和公司信息
					 * 减少数据库交互次数，提高性能
					 */
					isNewRegistry = true;

					// 优化：使用Prisma嵌套写入功能，一次性创建关联记录
					registry = await tx.shareholderRegistry.create({
						data: {
							fileName: data.fileName,
							recordCount: data.recordCount,
							registerDate: new Date(data.registerDate),
							companyCode: data.companyCode,
							organizationId: data.organizationId,
							userId: userId,
							// 内联创建公司信息，减少一次数据库交互
							companyInfo: {
								create: {
									organizationId: data.organizationId,
									companyCode: data.companyCode,
									companyName: data.companyInfo.companyName,
									totalShares: data.companyInfo.totalShares,
									totalShareholders:
										data.companyInfo.totalShareholders,
									totalInstitutions:
										data.companyInfo.totalInstitutions,
									largeSharesCount:
										data.companyInfo.largeSharesCount,
									institutionShares:
										data.companyInfo.institutionShares,
									largeShareholdersCount:
										data.companyInfo.largeShareholdersCount,
									registerDate: new Date(data.registerDate),
								},
							},
						},
						include: {
							companyInfo: true, // 包含创建的公司信息
						},
					});
				}

				/**
				 * 股东数据批量处理
				 *
				 * 优化策略：
				 * 1. 一次性查询所有可能存在的股东记录
				 * 2. 使用内存中的Map结构快速查找匹配记录
				 * 3. 分别收集需要创建和更新的记录
				 * 4. 使用批量操作减少数据库交互次数
				 */

				// 准备查询条件 - 提取所有股东ID和一码通账号用于批量查询
				const shareholderIds = data.shareholders.map(
					(s) => s.shareholderId
				);
				const unifiedAccountNumbers = data.shareholders.map(
					(s) => s.unifiedAccountNumber
				);

				/**
				 * 批量查询现有股东记录
				 *
				 * 使用复合条件一次性查询所有可能存在的股东记录：
				 * 1. 匹配名册ID、组织ID和登记日期
				 * 2. 使用OR条件匹配股东ID或(一码通账号+股东ID组合)
				 *
				 * 优化建议：考虑添加复合索引(registryId, organizationId, registerDate)提升查询性能
				 */
				const existingShareholders = await tx.shareholder.findMany({
					where: {
						registryId: registry.id,
						organizationId: data.organizationId,
						registerDate: new Date(data.registerDate),
						OR: [
							{ shareholderId: { in: shareholderIds } },
							{
								AND: [
									{
										unifiedAccountNumber: {
											in: unifiedAccountNumbers,
										},
									},
									{ shareholderId: { in: shareholderIds } },
								],
							},
						],
					},
				});

				/**
				 * 构建高效查找映射
				 *
				 * 使用Map数据结构存储现有股东记录，提供O(1)时间复杂度的查找
				 * 对所有股东类型统一使用"一码通账号_股东ID"作为复合键
				 *
				 * 性能提升：将O(n)的数组查找优化为O(1)的哈希查找
				 */
				const shareholderMap = new Map();
				for (const shareholder of existingShareholders) {
					// 对所有股东类型统一使用组合键
					shareholderMap.set(
						`${shareholder.unifiedAccountNumber}_${shareholder.shareholderId}`,
						shareholder
					);
				}

				// 准备批量创建和更新的数据集合
				const shareholdersToCreate: Array<Record<string, unknown>> = [];
				const shareholdersToUpdate: Array<Record<string, unknown>> = [];

				/**
				 * 股东记录处理循环
				 *
				 * 遍历所有股东数据，根据是否存在决定更新或创建
				 * 使用Map的快速查找能力，避免嵌套循环导致的O(n²)复杂度
				 */
				for (const shareholder of data.shareholders) {
					// 统一使用组合键进行查找
					const lookupKey = `${shareholder.unifiedAccountNumber}_${shareholder.shareholderId}`;

					// O(1)时间复杂度查找现有记录
					const existingShareholder = shareholderMap.get(lookupKey);

					if (existingShareholder) {
						/**
						 * 处理股东更新
						 *
						 * 只有在字段有变化时才进行更新，避免不必要的数据库操作
						 * 使用辅助函数处理字段比较和更新逻辑
						 */
						const updateData: Record<string, unknown> = {
							id: existingShareholder.id,
							shareholderId: existingShareholder.shareholderId,
						};

						// 使用辅助函数处理字段更新逻辑，返回是否需要更新
						const shouldUpdate = processShareholderUpdateFields(
							updateData,
							existingShareholder,
							shareholder,
							registryType
						);

						if (shouldUpdate) {
							shareholdersToUpdate.push(updateData);
						}
					} else {
						/**
						 * 处理新股东创建
						 *
						 * 收集所有需要创建的股东记录，准备批量插入
						 * 包含所有必要字段，确保数据完整性
						 */
						shareholdersToCreate.push({
							registryId: registry.id,
							organizationId: data.organizationId,
							shareholderId: shareholder.shareholderId,
							unifiedAccountNumber:
								shareholder.unifiedAccountNumber,
							securitiesAccountName:
								shareholder.securitiesAccountName,
							shareholderCategory:
								shareholder.shareholderCategory,
							numberOfShares: shareholder.numberOfShares,
							lockedUpShares: shareholder.lockedUpShares,
							shareholdingRatio: shareholder.shareholdingRatio,
							frozenShares: shareholder.frozenShares,
							cashAccount: shareholder.cashAccount,
							sharesInCashAccount:
								shareholder.sharesInCashAccount,
							marginAccount: shareholder.marginAccount,
							sharesInMarginAccount:
								shareholder.sharesInMarginAccount,
							contactAddress: shareholder.contactAddress,
							contactNumber: shareholder.contactNumber,
							zipCode: shareholder.zipCode,
							relatedPartyIndicator:
								shareholder.relatedPartyIndicator,
							clientCategory: shareholder.clientCategory,
							remarks: shareholder.remarks,
							registerDate: new Date(data.registerDate),
							// 添加05名册特有字段
							marginCollateralAccountNumber:
								shareholder.marginCollateralAccountNumber,
							marginCollateralAccountName:
								shareholder.marginCollateralAccountName,
							natureOfShares: shareholder.natureOfShares,
						});
					}
				}

				/**
				 * 批量创建股东记录
				 *
				 * 使用动态分批处理策略，根据数据量大小自动调整批处理参数：
				 * 1. 小数据量(<500)：使用较小批次(200)，减少单次内存占用
				 * 2. 中数据量(500-1000)：使用中等批次(400)，平衡性能和内存
				 * 3. 大数据量(>1000)：使用较大批次(600)，提高处理效率
				 *
				 * 性能提升：相比单条插入，批量插入可提升10-100倍性能
				 * 内存优化：根据数据量动态调整批次大小，避免内存压力
				 */
				if (shareholdersToCreate.length > 0) {
					// 根据数据量动态确定批处理大小
					let batchSize: number;
					const dataSize = shareholdersToCreate.length;

					// 动态批处理大小策略
					if (dataSize < 500) {
						// 小数据量：使用较小批次，减少单次内存占用
						batchSize = 200;
					} else if (dataSize < 1000) {
						// 中数据量：使用中等批次，平衡性能和内存
						batchSize = 400;
					} else {
						// 大数据量：使用较大批次，提高处理效率
						batchSize = 600;
					}
					// 分批执行创建操作
					for (
						let i = 0;
						i < shareholdersToCreate.length;
						i += batchSize
					) {
						const batch = shareholdersToCreate.slice(
							i,
							i + batchSize
						);
						await tx.shareholder.createMany({
							data: batch as any[], // 类型断言解决类型不匹配问题
							skipDuplicates: true, // 跳过重复记录，避免唯一键冲突
						});
					}
				}

				/**
				 * 批量更新股东记录
				 *
				 * 使用Promise.all并行处理多个更新操作，但增加并发控制：
				 * 1. 根据更新数据量动态调整并发数
				 * 2. 将更新操作分批并行执行
				 * 3. 使用复合主键条件确保准确更新
				 *
				 * 性能提升：并行执行可减少约50-80%的等待时间
				 * 资源优化：动态控制并发数，避免数据库连接耗尽
				 */
				if (shareholdersToUpdate.length > 0) {
					// 根据数据量动态确定并发批次大小
					let concurrencyLimit: number;
					const updateSize = shareholdersToUpdate.length;

					// 动态并发限制策略
					if (updateSize < 100) {
						// 小数据量：较高并发
						concurrencyLimit = 20;
					} else if (updateSize < 500) {
						// 中数据量：中等并发
						concurrencyLimit = 15;
					} else {
						// 大数据量：较低并发，避免资源耗尽
						concurrencyLimit = 10;
					}

					// 分批并行处理更新
					for (
						let i = 0;
						i < shareholdersToUpdate.length;
						i += concurrencyLimit
					) {
						const updateBatch = shareholdersToUpdate.slice(
							i,
							i + concurrencyLimit
						);

						// 并行执行当前批次的更新操作
						await Promise.all(
							updateBatch.map((updateData) => {
								const { id, shareholderId, ...data } =
									updateData;
								return tx.shareholder.update({
									where: {
										shareholderId_id: {
											shareholderId:
												shareholderId as string,
											id: id as string,
										},
									},
									data,
								});
							})
						);
					}
				}

				/**
				 * 名册更新后处理
				 *
				 * 对于更新现有名册的情况，需要执行额外处理：
				 * 1. 重新计算实际股东数量
				 * 2. 更新公司信息(仅限01类型名册)
				 * 3. 更新名册记录数为实际合并后的数量
				 */
				if (!isNewRegistry) {
					/**
					 * 并行处理计数和更新
					 *
					 * 使用Promise.all同时执行两个操作：
					 * 1. 查询实际股东数量
					 * 2. 根据名册类型选择性更新公司信息
					 *
					 * 性能提升：减少串行等待时间
					 */
					const [actualShareholderCount] = await Promise.all([
						// 查询当前实际的股东记录数量
						tx.shareholder.count({
							where: {
								registryId: registry.id,
								organizationId: data.organizationId,
								registerDate: new Date(data.registerDate),
							},
						}),

						// 如果是01名册，同时更新公司信息
						// 01名册包含完整公司信息，05名册仅包含部分信息
						registryType === "01"
							? tx.companyInfo.updateMany({
									where: {
										registryId: registry.id,
										organizationId: data.organizationId,
									},
									data: {
										totalShareholders:
											data.companyInfo.totalShareholders,
										totalShares:
											data.companyInfo.totalShares,
										totalInstitutions:
											data.companyInfo.totalInstitutions,
										largeSharesCount:
											data.companyInfo.largeSharesCount,
										institutionShares:
											data.companyInfo.institutionShares,
										largeShareholdersCount:
											data.companyInfo
												.largeShareholdersCount,
										companyName:
											data.companyInfo.companyName,
									},
							  })
							: Promise.resolve(), // 如果不是01名册，返回一个已解决的Promise
					]);

					// 更新名册记录数为实际合并后的股东数量
					await tx.shareholderRegistry.update({
						where: { id: registry.id },
						data: { recordCount: actualShareholderCount },
					});
				}

				return registry;
			},
			{
				/**
				 * 事务配置
				 *
				 * timeout: 动态设置事务超时时间，根据处理的数据量自动调整
				 * isolationLevel: 设置事务隔离级别，影响并发读写行为
				 */
				timeout: dynamicTimeout,
				isolationLevel: "ReadCommitted", // 允许读取已提交的数据，避免脏读
			}
		);
      
      // 确保result不为undefined
      if (!result) {
        throw new HTTPException(500, { message: "处理股东名册数据失败" });
      }
      
      /**
       * 响应数据构建
       * 
       * 从事务结果中提取必要字段，构建标准响应格式
       * 日期格式化为YYYY-MM-DD格式，便于前端处理
       */
      const responseData: UploadRegistryResponse = {
        id: result.id,
        fileName: result.fileName,
        recordCount: result.recordCount,
        registerDate: result.registerDate.toISOString().split('T')[0],
        uploadedAt: result.uploadedAt.toISOString(),
      };
      
      // 使用成功响应函数返回结果
      successResponse(c, "股东名册上传成功", responseData);
      
      // 不再直接返回响应，而是让中间件处理
      return;
    } catch (error) {
      // 使用统一的异常处理函数
      handleHttpException(c, error);
      return;
    }
  }
); 

