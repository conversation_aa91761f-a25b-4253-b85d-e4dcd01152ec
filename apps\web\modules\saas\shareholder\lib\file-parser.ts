/**
 * 通用股东名册文件解析器
 * 支持多种文件格式的解析，包括DBF、XLS、XLSX和ZIP
 * 支持01和05名册的解析和合并
 *
 * 主要功能：
 * 1. 根据文件类型选择适当的解析方法
 * 2. 对不同格式的文件进行标准化处理，统一输出格式
 * 3. 支持ZIP文件的解压和内部文件解析
 * 4. 支持Excel文件(XLS/XLSX)的表格数据提取
 * 5. 支持01和05名册的字段映射和数据合并
 * 
 * 使用场景：
 * - 用于解析多种格式的股东名册文件
 * - 支持证券公司提供的不同格式的股东名册
 * - 兼容现有系统的数据导入流程
 */

import * as XLSX from "xlsx";
import JSZip from "jszip";
import { parseDBFFile } from "./dbf-parser";
import type { ShareholderRegistryParseResult } from "./dbf-parser-common";

/**
 * 检查文件类型是否为支持的格式
 * 
 * @param fileName 文件名
 * @returns 是否为支持的文件格式
 */
export function isSupportedFileType(fileName: string): boolean {
  const lowercaseName = fileName.toLowerCase();
  return lowercaseName.endsWith(".dbf");
}

/**
 * 检查文件类型是否为Excel文件(XLS/XLSX)
 *
 * @param fileName 文件名
 * @returns 是否为Excel文件
 */
function isExcelFile(fileName: string): boolean {
  const lowercaseName = fileName.toLowerCase();
  return lowercaseName.endsWith(".xls") || lowercaseName.endsWith(".xlsx");
}

/**
 * 检查文件类型是否为ZIP文件
 *
 * @param fileName 文件名
 * @returns 是否为ZIP文件
 */
function isZipFile(fileName: string): boolean {
  const lowercaseName = fileName.toLowerCase();
  return lowercaseName.endsWith(".zip");
}

/**
 * 检查文件类型是否为DBF文件
 *
 * @param fileName 文件名
 * @returns 是否为DBF文件
 */
function isDbfFile(fileName: string): boolean {
  const lowercaseName = fileName.toLowerCase();
  return lowercaseName.endsWith(".dbf");
}

/**
 * 从Excel文件解析股东名册数据
 * 
 * @param file Excel文件(XLS/XLSX)
 * @returns 解析结果
 */
async function parseExcelFile(file: File): Promise<ShareholderRegistryParseResult> {
  try {
    // 读取Excel文件数据
    const buffer = await file.arrayBuffer();
    const workbook = XLSX.read(buffer, { type: "array" });
    
    // 获取第一个工作表
    const firstSheetName = workbook.SheetNames[0];
    const worksheet = workbook.Sheets[firstSheetName];
    
    // 将工作表转换为JSON对象数组
    const rawData = XLSX.utils.sheet_to_json(worksheet);
    
    // 如果没有数据，返回错误
    if (!rawData || rawData.length === 0) {
      return {
        success: false,
        fileName: file.name,
        error: {
          type: "FILE_ERROR",
          message: "Excel文件不包含有效数据",
        },
      };
    }
    
    // 分析文件名，提取公司代码和报告日期
    const { companyCode, registerDate } = parseFileName(file.name);
    
    // 尝试识别特殊记录（通常是表头或汇总行）
    const specialRecords = identifySpecialRecords(rawData);
    const regularRecords = extractRegularRecords(rawData, specialRecords);
    
    // 检查是否包含必要字段
    const missingFields = checkRequiredFields(regularRecords[0]);
    if (missingFields.length > 0) {
      return {
        success: false,
        fileName: file.name,
        error: {
          type: "MISSING_FIELDS",
          message: `名册中缺少字段"${missingFields.join('", "')}"`,
          details: missingFields,
        },
      };
    }
    
    // 提取公司信息
    const companyInfo = extractCompanyInfo(specialRecords);
    
    // 转换为标准格式的股东记录
    const shareholders = convertToStandardFormat(regularRecords);
    
    return {
      success: true,
      fileName: file.name,
      companyCode: companyCode || companyInfo.companyCode,
      registerDate: registerDate || companyInfo.registerDate,
      records: shareholders,
      recordCount: shareholders.length,
      companyInfo: {
        companyName: companyInfo.companyName || "",
        totalShares: companyInfo.totalShares || "0",
        totalShareholders: companyInfo.totalShareholders || 0,
        totalInstitutions: companyInfo.totalInstitutions || 0,
        institutionShares: companyInfo.institutionShares || "0",
        largeSharesCount: companyInfo.largeSharesCount || "0",
        largeShareholdersCount: Number(companyInfo.largeSharesCount || 0),
      },
    };
  } catch (error) {
    // 不打印到控制台，直接返回错误
    return {
      success: false,
      fileName: file.name,
      error: {
        type: "FILE_ERROR",
        message: error instanceof Error ? error.message : "解析Excel文件失败",
      },
    };
  }
}

/**
 * 从ZIP文件中提取并解析股东名册文件
 * 
 * @param file ZIP文件
 * @returns 解析结果
 */
async function parseZipFile(file: File): Promise<ShareholderRegistryParseResult> {
  try {
    // 读取ZIP文件
    const arrayBuffer = await file.arrayBuffer();
    const zip = new JSZip();
    const zipContents = await zip.loadAsync(arrayBuffer);
    
    // 查找支持的文件
    let foundFile: { name: string; content: Blob } | null = null;
    
    // 首先查找DBF文件
    for (const fileName in zipContents.files) {
      if (isDbfFile(fileName) && !zipContents.files[fileName].dir) {
        const content = await zipContents.files[fileName].async("blob");
        foundFile = { name: fileName, content };
        break;
      }
    }
    
    // 如果没有找到DBF文件，查找Excel文件
    if (!foundFile) {
      for (const fileName in zipContents.files) {
        if (isExcelFile(fileName) && !zipContents.files[fileName].dir) {
          const content = await zipContents.files[fileName].async("blob");
          foundFile = { name: fileName, content };
          break;
        }
      }
    }
    
    // 如果没有找到支持的文件，返回错误
    if (!foundFile) {
      return {
        success: false,
        fileName: file.name,
        error: {
          type: "FILE_ERROR",
          message: "ZIP文件中未找到有效的DBF或Excel文件",
        },
      };
    }
    
    // 根据找到的文件类型调用相应的解析方法
    const extractedFile = new File([foundFile.content], foundFile.name);
    
    if (isDbfFile(foundFile.name)) {
      return await parseDBFFile(extractedFile);
    }
    
    if (isExcelFile(foundFile.name)) {
      return await parseExcelFile(extractedFile);
    }
    
    return {
      success: false,
      fileName: file.name,
      error: {
        type: "FILE_ERROR",
        message: "ZIP文件中的文件格式不受支持",
      },
    };
  } catch (error) {
    // 不打印到控制台，直接返回错误
    return {
      success: false,
      fileName: file.name,
      error: {
        type: "FILE_ERROR",
        message: error instanceof Error ? error.message : "解析ZIP文件失败",
      },
    };
  }
}

/**
 * 从文件名解析公司代码和报告日期
 * 从文件名中提取公司代码和报告日期信息
 *
 * @param fileName 文件名，标准格式为DQMC01_001339_20240930.DBF或DQMC05_001339_20240930.DBF或任何包含公司代码和日期的文件名
 * @returns 解析结果对象，包含公司代码和报告日期
 */
export function parseFileName(fileName: string): {
	companyCode?: string;
	registerDate?: string;
} {
	try {
		// 尝试从文件名中提取公司代码和日期
		// 通用的模式：检查是否有6位数字作为公司代码，8位数字作为日期

		// 移除扩展名
		const nameWithoutExt = fileName.split(".")[0];

		// 查找可能的公司代码模式（6位数字）
		const companyCodeMatch = nameWithoutExt.match(/[_-]?(\d{6})[_-]?/);
		const companyCode = companyCodeMatch ? companyCodeMatch[1] : undefined;

		// 查找可能的日期模式（8位数字：YYYYMMDD）
		const dateMatch = nameWithoutExt.match(/[_-]?(\d{8})[_-]?/);
		let registerDate: string | undefined;

		if (dateMatch) {
			const dateStr = dateMatch[1];
			const year = dateStr.substring(0, 4);
			const month = dateStr.substring(4, 6);
			const day = dateStr.substring(6, 8);

			// 验证日期有效性
			const date = new Date(Number(year), Number(month) - 1, Number(day));
			if (
				date.getFullYear() === Number(year) &&
				date.getMonth() === Number(month) - 1 &&
				date.getDate() === Number(day)
			) {
				registerDate = `${year}-${month}-${day}`;
			}
		}

		return { companyCode, registerDate };
	} catch (error) {
		// 不打印到控制台，直接返回空结果
		return {};
	}
}

/**
 * 识别Excel数据中的特殊记录
 * 
 * @param data Excel数据数组
 * @returns 特殊记录对象
 */
function identifySpecialRecords(data: any[]): {
  headerRow?: any;
  summaryRows: any[];
} {
  const specialRecords = {
    headerRow: undefined,
    summaryRows: [] as any[]
  };
  
  // 通常第一行是标题行
  if (data.length > 0) {
    specialRecords.headerRow = data[0];
  }
  
  // 查找可能的汇总行（通常在最后）
  if (data.length > 2) {
    // 检查最后几行是否有汇总信息的特征
    const lastRows = data.slice(-3);
    for (const row of lastRows) {
      // 检查是否包含"合计"、"总计"、"汇总"等关键词
      const rowValues = Object.values(row).map(val => 
        val && typeof val === 'string' ? val.toString() : ''
      );
      
      const summaryKeywords = ['合计', '总计', '汇总', '总户数', '机构'];
      
      if (summaryKeywords.some(keyword => 
        rowValues.some(value => value.includes(keyword))
      )) {
        specialRecords.summaryRows.push(row);
      }
    }
  }
  
  return specialRecords;
}

/**
 * 从Excel数据中提取正常股东记录
 * 
 * @param data 所有Excel数据
 * @param specialRecords 特殊记录
 * @returns 正常股东记录数组
 */
function extractRegularRecords(data: any[], specialRecords: { headerRow?: any; summaryRows: any[] }): any[] {
  // 创建一个特殊记录的集合，用于快速查找
  const specialRecordsSet = new Set();
  
  if (specialRecords.headerRow) {
    specialRecordsSet.add(specialRecords.headerRow);
  }
  
  specialRecords.summaryRows.forEach(row => {
    specialRecordsSet.add(row);
  });
  
  // 过滤出正常记录
  return data.filter(row => !specialRecordsSet.has(row));
}

/**
 * 检查记录是否包含必需字段
 * 
 * @param record 记录对象
 * @returns 缺失的字段名数组
 */
function checkRequiredFields(record: any): string[] {
  // 必填字段的中文名和可能的字段名映射
  const requiredFieldMappings = [
    { label: "一码通账户号码", possibleNames: ["YMTH", "一码通号码", "一码通账户号码", "一码通账号"] },
    { label: "证券账户名称", possibleNames: ["ZQZHMC", "XYZQZHMC", "证券账户名称", "股东姓名", "投资者名称", "信用证券账户名称"] },
    { label: "证件号码", possibleNames: ["ZJDM", "XYZQZHZJDM", "证件代码", "证件号码", "身份证号码", "证件号", "信用证券账户证件号码"] },
    { label: "持有人类别", possibleNames: ["CYRLBMS", "持有人类别", "股东类别", "投资者类别"] }
  ];
  
  const missingFields: string[] = [];
  
  if (!record) {
    return requiredFieldMappings.map(mapping => mapping.label);
  }
  
  // 检查每个必填字段
  for (const fieldMapping of requiredFieldMappings) {
    // 检查是否有任何一个可能的字段名存在
    const hasField = fieldMapping.possibleNames.some(name => 
      record[name] !== undefined || 
      // 也检查小写和大写版本
      record[name.toLowerCase()] !== undefined || 
      record[name.toUpperCase()] !== undefined
    );
    
    if (!hasField) {
      missingFields.push(fieldMapping.label);
    }
  }
  
  return missingFields;
}

/**
 * 从特殊记录中提取公司信息
 * 
 * @param specialRecords 特殊记录对象
 * @returns 公司信息
 */
function extractCompanyInfo(specialRecords: {
	headerRow?: any;
	summaryRows: any[];
}): {
	companyName: string;
	companyCode: string;
	registerDate: string;
	totalShares: string;
	totalShareholders: number;
	totalInstitutions: number;
	institutionShares: string;
	largeSharesCount: string;
	largeShareholdersCount: number;
} {
	// 默认值
	const companyInfo = {
		companyName: "",
		companyCode: "",
		registerDate: "",
		totalShares: "0",
		totalShareholders: 0,
		totalInstitutions: 0,
		institutionShares: "0",
		largeSharesCount: "0",
		largeShareholdersCount: 0,
	};

	try {
		// 从汇总行提取信息
		if (specialRecords.summaryRows.length > 0) {
			for (const row of specialRecords.summaryRows) {
				// 提取总股份数和股东数量
				for (const key in row) {
					const value = row[key];
					const keyLower = String(key).toLowerCase();

					// 提取公司名称
					if (
						keyLower.includes("公司") ||
						keyLower.includes("名称")
					) {
						companyInfo.companyName = String(value || "");
					}

					// 提取总股份
					if (
						keyLower.includes("总股") ||
						keyLower.includes("股份总数")
					) {
						companyInfo.totalShares = String(value || "0");
					}

					// 提取股东总数
					if (
						keyLower.includes("股东总数") ||
						keyLower.includes("总户数")
					) {
						companyInfo.totalShareholders = Number(value || 0);
					}

					// 提取机构数量
					if (
						keyLower.includes("机构") &&
						keyLower.includes("数量")
					) {
						companyInfo.totalInstitutions = Number(value || 0);
					}

					// 提取大股东数量
					if (
						(keyLower.includes("大") ||
							keyLower.includes("重要")) &&
						keyLower.includes("股东")
					) {
						companyInfo.largeShareholdersCount = Number(value || 0);
					}
				}
			}
		}

		return companyInfo;
	} catch (error) {
		// 不打印到控制台，返回默认对象
		return companyInfo;
	}
}

/**
 * 将各种格式的记录转换为标准格式
 * 支持01和05名册的字段映射
 * 
 * @param records 原始记录数组
 * @returns 标准格式的股东记录数组
 */
function convertToStandardFormat(records: any[]): any[] {
  return records.map((record, index) => {
    // 字段名映射表：将不同可能的字段名映射到标准字段名
    const fieldMappings: Record<string, string[]> = {
      // 01和05名册共有字段
      "unifiedAccountNumber": ["YMTH", "一码通号码", "一码通账户号码", "一码通账号"],
      "shareholderCategory": ["CYRLBMS", "持有人类别", "股东类别", "投资者类别"],
      "contactAddress": ["TXDZ", "联系地址", "通讯地址", "地址"],
      "frozenShares": ["DJGS", "冻结股份", "冻结股数", "冻结(质押/回购)数量"],
      
      // 01名册特有字段
      "shareholderId": ["ZJDM", "证件代码", "证件号码", "身份证号码", "证件号"],
      "securitiesAccountName": ["ZQZHMC", "证券账户名称", "股东姓名", "投资者名称"],
      "numberOfShares": ["CGSL", "持股数量", "持股", "股数"],
      "lockedUpShares": ["XSGSL", "限售股份", "限售股数"],
      "shareholdingRatio": ["CGBL", "持股比例", "占比"],
      "cashAccount": ["PTZQZH", "普通证券账户", "普通账户"],
      "sharesInCashAccount": ["PTZHCGSL", "普通账户持股", "普通账户股数"],
      "contactNumber": ["DHHM", "电话号码", "联系电话", "电话"],
      "zipCode": ["YZBM", "邮政编码", "邮编"],
      "relatedPartyIndicator": ["GLGXBS", "关联关系", "关联方标识"],
      "clientCategory": ["KHLB", "客户类别"],
      "remarks": ["BZ", "备注"],
      
      // 05名册特有字段
      "xyzqzhzjdm": ["XYZHZJDM", "信用证券账户证件号码"],
      "xyzqzhmc": ["XYZHMC", "信用证券账户名称"],
      "marginAccount": ["XYZQZH", "信用证券账户", "信用账户"],
      "sharesInMarginAccount": ["XYZHCGSL", "信用账户持股", "信用账户股数", "CGSL"], // 05名册中CGSL表示信用账户持股
      "marginCollateralAccountNumber": ["HZZQZH", "汇总证券账户", "汇总账户号码"],
      "marginCollateralAccountName": ["HZZHMC", "汇总证券账户名称", "汇总账户名称"],
      "natureOfShares": ["GFXZ", "股份性质"]
    };
    
    // 创建标准格式的记录
    const standardRecord: Record<string, any> = {};
    
    // 为每个标准字段寻找匹配的源字段
    for (const [standardField, possibleSourceFields] of Object.entries(fieldMappings)) {
      // 检查记录中是否有任何一个可能的源字段
      for (const sourceField of possibleSourceFields) {
        // 尝试多种大小写变体
        const variants = [
          sourceField, 
          sourceField.toLowerCase(), 
          sourceField.toUpperCase()
        ];
        
        for (const variant of variants) {
          if (record[variant] !== undefined) {
            // 根据字段类型处理值
            const value = record[variant];
            
            // 确保数值字段为字符串类型
            if (["numberOfShares", "lockedUpShares", "shareholdingRatio", "frozenShares", 
                 "sharesInCashAccount", "sharesInMarginAccount"].includes(standardField)) {
              standardRecord[standardField] = String(value || "0");
            } else {
              standardRecord[standardField] = value;
            }
            
            break;
          }
        }
      }
    }
    
    // 处理05名册特有字段的映射到标准字段
    // 如果是05名册（通过检查05名册特有字段判断）
    if (standardRecord.xyzqzhzjdm && !standardRecord.shareholderId) {
      standardRecord.shareholderId = standardRecord.xyzqzhzjdm;
    }
    
    if (standardRecord.xyzqzhmc && !standardRecord.securitiesAccountName) {
      standardRecord.securitiesAccountName = standardRecord.xyzqzhmc;
    }
    
    // 05名册中CGSL表示信用账户持股，需要特殊处理
    if (record.CGSL !== undefined && !standardRecord.numberOfShares && standardRecord.sharesInMarginAccount) {
      // 05名册中，信用账户持股数量就是总持股数量
      standardRecord.numberOfShares = standardRecord.sharesInMarginAccount;
    }
    
    // 清理临时字段 - 避免使用 delete 操作符
    const { xyzqzhzjdm, xyzqzhmc, ...cleanedRecord } = standardRecord;
    
    // 添加索引作为行号
    cleanedRecord.XH = index + 1;
    
    return cleanedRecord;
  });
}

/**
 * 解析上传的文件为股东名册数据
 * 主入口函数，根据文件类型选择适当的解析方法
 * 
 * @param file 上传的文件（仅支持DBF格式）
 * @returns 股东名册解析结果
 */
export async function parseShareholderRegistryFile(file: File): Promise<ShareholderRegistryParseResult> {
  try {
    if (!isSupportedFileType(file.name)) {
      return {
        success: false,
        fileName: file.name,
        error: {
          type: "FILE_ERROR",
          message: "不支持的文件类型，请上传DBF格式的文件",
        },
      };
    }
    
    // 只处理DBF文件
    return await parseDBFFile(file);
    
  } catch (error) {
    // 不打印到控制台，直接返回错误信息
    return {
      success: false,
      fileName: file.name,
      error: {
        type: "FILE_ERROR",
        message: error instanceof Error ? error.message : "解析文件失败",
      },
    };
  }
} 